// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: tensorflow_serving/predict.proto

package tensorflow_serving

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	framework "rtb_model_server/proto/tensorflow/core/framework"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Deterministic mode for the request. When specified, model servers will
// reduce numeric instability based on different mode selections.
type PredictRequest_RequestOptions_DeterministicMode int32

const (
	PredictRequest_RequestOptions_DETERMINISTIC_MODE_UNSPECIFIED PredictRequest_RequestOptions_DeterministicMode = 0
	// Only supported in disaggregated serving. When set, the request will be
	// pinned to a fixed decoder slot index that's deterministic across
	// processes.
	PredictRequest_RequestOptions_FIXED_DECODER_SLOT PredictRequest_RequestOptions_DeterministicMode = 1
)

// Enum value maps for PredictRequest_RequestOptions_DeterministicMode.
var (
	PredictRequest_RequestOptions_DeterministicMode_name = map[int32]string{
		0: "DETERMINISTIC_MODE_UNSPECIFIED",
		1: "FIXED_DECODER_SLOT",
	}
	PredictRequest_RequestOptions_DeterministicMode_value = map[string]int32{
		"DETERMINISTIC_MODE_UNSPECIFIED": 0,
		"FIXED_DECODER_SLOT":             1,
	}
)

func (x PredictRequest_RequestOptions_DeterministicMode) Enum() *PredictRequest_RequestOptions_DeterministicMode {
	p := new(PredictRequest_RequestOptions_DeterministicMode)
	*p = x
	return p
}

func (x PredictRequest_RequestOptions_DeterministicMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PredictRequest_RequestOptions_DeterministicMode) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_serving_predict_proto_enumTypes[0].Descriptor()
}

func (PredictRequest_RequestOptions_DeterministicMode) Type() protoreflect.EnumType {
	return &file_tensorflow_serving_predict_proto_enumTypes[0]
}

func (x PredictRequest_RequestOptions_DeterministicMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PredictRequest_RequestOptions_DeterministicMode.Descriptor instead.
func (PredictRequest_RequestOptions_DeterministicMode) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_serving_predict_proto_rawDescGZIP(), []int{0, 1, 0}
}

// Request state is used to control handling of individual streamed requests.
//
// SPLIT and END_SPLIT are used to handle splitting of requests.  NONE is the
// default when the stream request is not split and used for a single-turn,
// single request.
//
// CANCEL is used for early termination of a stream. It can be sent anytime to
// cancel the stream.
//
// SPLIT is used when multiple streamed requests are used to generate a
// logical request. END_SPLIT should be called for the last split of the
// multi-turn request to start the processing of the current turn. NONE can
// not be interspersed with SPLIT and END_SPLIT messages.
// If another request is sent on the same stream after END_SPLIT, it can be
// either SPLIT or END_SPLIT to start accumulating input or trigger the next
// model turn respectively.
type PredictStreamedOptions_RequestState int32

const (
	PredictStreamedOptions_NONE      PredictStreamedOptions_RequestState = 0
	PredictStreamedOptions_SPLIT     PredictStreamedOptions_RequestState = 1
	PredictStreamedOptions_END_SPLIT PredictStreamedOptions_RequestState = 2
	PredictStreamedOptions_CANCEL    PredictStreamedOptions_RequestState = 3
)

// Enum value maps for PredictStreamedOptions_RequestState.
var (
	PredictStreamedOptions_RequestState_name = map[int32]string{
		0: "NONE",
		1: "SPLIT",
		2: "END_SPLIT",
		3: "CANCEL",
	}
	PredictStreamedOptions_RequestState_value = map[string]int32{
		"NONE":      0,
		"SPLIT":     1,
		"END_SPLIT": 2,
		"CANCEL":    3,
	}
)

func (x PredictStreamedOptions_RequestState) Enum() *PredictStreamedOptions_RequestState {
	p := new(PredictStreamedOptions_RequestState)
	*p = x
	return p
}

func (x PredictStreamedOptions_RequestState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PredictStreamedOptions_RequestState) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_serving_predict_proto_enumTypes[1].Descriptor()
}

func (PredictStreamedOptions_RequestState) Type() protoreflect.EnumType {
	return &file_tensorflow_serving_predict_proto_enumTypes[1]
}

func (x PredictStreamedOptions_RequestState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PredictStreamedOptions_RequestState.Descriptor instead.
func (PredictStreamedOptions_RequestState) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_serving_predict_proto_rawDescGZIP(), []int{1, 0}
}

// PredictRequest specifies which TensorFlow model to run, as well as
// how inputs are mapped to tensors and how outputs are filtered before
// returning to user.
type PredictRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Model Specification. If version is not specified, will use the latest
	// (numerical) version.
	ModelSpec *ModelSpec `protobuf:"bytes,1,opt,name=model_spec,json=modelSpec,proto3" json:"model_spec,omitempty"`
	// Input tensors.
	// Names of input tensor are alias names. The mapping from aliases to real
	// input tensor names is stored in the SavedModel export as a prediction
	// SignatureDef under the 'inputs' field.
	Inputs map[string]*framework.TensorProto `protobuf:"bytes,2,rep,name=inputs,proto3" json:"inputs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Output filter.
	// Names specified are alias names. The mapping from aliases to real output
	// tensor names is stored in the SavedModel export as a prediction
	// SignatureDef under the 'outputs' field.
	// Only tensors specified here will be run/fetched and returned, with the
	// exception that when none is specified, all tensors specified in the
	// named signature will be run/fetched and returned.
	OutputFilter []string `protobuf:"bytes,3,rep,name=output_filter,json=outputFilter,proto3" json:"output_filter,omitempty"`
	// Options for streaming requests to control how multiple requests/responses
	// are handled within a single stream.
	PredictStreamedOptions *PredictStreamedOptions `protobuf:"bytes,5,opt,name=predict_streamed_options,json=predictStreamedOptions,proto3" json:"predict_streamed_options,omitempty"`
	// Client identifier to group requests belonging to a specific entity.
	// Example entities can be product ids, service names, user ids etc.
	// Servers can use this to optimize placement, caching and colocation.
	// TODO(b/329897437): Migrate to client_id in RequestOptions.
	ClientId       []byte                         `protobuf:"bytes,6,opt,name=client_id,json=clientId,proto3,oneof" json:"client_id,omitempty"`
	RequestOptions *PredictRequest_RequestOptions `protobuf:"bytes,7,opt,name=request_options,json=requestOptions,proto3,oneof" json:"request_options,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PredictRequest) Reset() {
	*x = PredictRequest{}
	mi := &file_tensorflow_serving_predict_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PredictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictRequest) ProtoMessage() {}

func (x *PredictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_serving_predict_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictRequest.ProtoReflect.Descriptor instead.
func (*PredictRequest) Descriptor() ([]byte, []int) {
	return file_tensorflow_serving_predict_proto_rawDescGZIP(), []int{0}
}

func (x *PredictRequest) GetModelSpec() *ModelSpec {
	if x != nil {
		return x.ModelSpec
	}
	return nil
}

func (x *PredictRequest) GetInputs() map[string]*framework.TensorProto {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *PredictRequest) GetOutputFilter() []string {
	if x != nil {
		return x.OutputFilter
	}
	return nil
}

func (x *PredictRequest) GetPredictStreamedOptions() *PredictStreamedOptions {
	if x != nil {
		return x.PredictStreamedOptions
	}
	return nil
}

func (x *PredictRequest) GetClientId() []byte {
	if x != nil {
		return x.ClientId
	}
	return nil
}

func (x *PredictRequest) GetRequestOptions() *PredictRequest_RequestOptions {
	if x != nil {
		return x.RequestOptions
	}
	return nil
}

// Options only used for streaming requests that control how inputs/ouputs are
// handled in the stream.
type PredictStreamedOptions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Request state used to handle segmentation of requests.
	// Only supported in disaggregated serving.
	RequestState PredictStreamedOptions_RequestState `protobuf:"varint,1,opt,name=request_state,json=requestState,proto3,enum=tensorflow.serving.PredictStreamedOptions_RequestState" json:"request_state,omitempty"`
	// Input tensors split dimensions.
	// Defines the dimension used to split input tensors specified
	// in PredictRequest.inputs. The dimension will be used
	// for concatenation of multiple SPLIT requests.
	//
	// For input tensor in PredictRequest.inputs that are not contained in this
	// map, the tensors from the first SPLIT request will be used.
	//
	// For example, with an original input tensor of [[1, 2, 3, 4], [5, 6, 7, 8]].
	//
	// For a split dimension of 0 and two requests (SPLIT and END_SPLIT), the
	// input tensors for request 1 should be [1, 2, 3, 4] and request 2 should be
	// be [5, 6, 7, 8].
	//
	// For a split dimension of 1 and two requests (SPLIT and END_SPLIT), the
	// input tensors for request 1 should be [[1, 2], [5, 6]] and request 2 should
	// be [[3, 4], [7, 8]].
	// This field is ignored if request_state is NONE.
	SplitDimensions map[string]int32 `protobuf:"bytes,2,rep,name=split_dimensions,json=splitDimensions,proto3" json:"split_dimensions,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// If true, there will be a single PredictResponse output.
	// If false, output can be split into 1 or more PredictResponses.
	// Value of this field should be the same for all requests in the stream.
	// Only supported in disaggregated serving.
	ReturnSingleResponse bool `protobuf:"varint,3,opt,name=return_single_response,json=returnSingleResponse,proto3" json:"return_single_response,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PredictStreamedOptions) Reset() {
	*x = PredictStreamedOptions{}
	mi := &file_tensorflow_serving_predict_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PredictStreamedOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictStreamedOptions) ProtoMessage() {}

func (x *PredictStreamedOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_serving_predict_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictStreamedOptions.ProtoReflect.Descriptor instead.
func (*PredictStreamedOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_serving_predict_proto_rawDescGZIP(), []int{1}
}

func (x *PredictStreamedOptions) GetRequestState() PredictStreamedOptions_RequestState {
	if x != nil {
		return x.RequestState
	}
	return PredictStreamedOptions_NONE
}

func (x *PredictStreamedOptions) GetSplitDimensions() map[string]int32 {
	if x != nil {
		return x.SplitDimensions
	}
	return nil
}

func (x *PredictStreamedOptions) GetReturnSingleResponse() bool {
	if x != nil {
		return x.ReturnSingleResponse
	}
	return false
}

// Response for PredictRequest on successful run.
type PredictResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Effective Model Specification used to process PredictRequest.
	ModelSpec *ModelSpec `protobuf:"bytes,2,opt,name=model_spec,json=modelSpec,proto3" json:"model_spec,omitempty"`
	// Output tensors.
	Outputs       map[string]*framework.TensorProto `protobuf:"bytes,1,rep,name=outputs,proto3" json:"outputs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PredictResponse) Reset() {
	*x = PredictResponse{}
	mi := &file_tensorflow_serving_predict_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PredictResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictResponse) ProtoMessage() {}

func (x *PredictResponse) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_serving_predict_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictResponse.ProtoReflect.Descriptor instead.
func (*PredictResponse) Descriptor() ([]byte, []int) {
	return file_tensorflow_serving_predict_proto_rawDescGZIP(), []int{2}
}

func (x *PredictResponse) GetModelSpec() *ModelSpec {
	if x != nil {
		return x.ModelSpec
	}
	return nil
}

func (x *PredictResponse) GetOutputs() map[string]*framework.TensorProto {
	if x != nil {
		return x.Outputs
	}
	return nil
}

// Options for PredictRequest.
type PredictRequest_RequestOptions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Client identifier to group requests belonging to a specific entity.
	// Example entities can be product ids, service names, user ids etc.
	// Servers can use this to optimize placement, caching and colocation.
	ClientId          []byte                                           `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3,oneof" json:"client_id,omitempty"`
	DeterministicMode *PredictRequest_RequestOptions_DeterministicMode `protobuf:"varint,2,opt,name=deterministic_mode,json=deterministicMode,proto3,enum=tensorflow.serving.PredictRequest_RequestOptions_DeterministicMode,oneof" json:"deterministic_mode,omitempty"`
	// Only supported in disaggregated serving. When set, additional arrays from
	// prefill will be returned if available.
	ReturnAdditionalArraysFromPrefill *bool `protobuf:"varint,3,opt,name=return_additional_arrays_from_prefill,json=returnAdditionalArraysFromPrefill,proto3,oneof" json:"return_additional_arrays_from_prefill,omitempty"`
	// Only supported in disaggregated serving. Returns these stop tokens in
	// response if the model stops at them. The model may stop at other tokens,
	// but will not return them in the response.
	ReturnStoptokens []int64 `protobuf:"varint,4,rep,packed,name=return_stoptokens,json=returnStoptokens,proto3" json:"return_stoptokens,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PredictRequest_RequestOptions) Reset() {
	*x = PredictRequest_RequestOptions{}
	mi := &file_tensorflow_serving_predict_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PredictRequest_RequestOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictRequest_RequestOptions) ProtoMessage() {}

func (x *PredictRequest_RequestOptions) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_serving_predict_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictRequest_RequestOptions.ProtoReflect.Descriptor instead.
func (*PredictRequest_RequestOptions) Descriptor() ([]byte, []int) {
	return file_tensorflow_serving_predict_proto_rawDescGZIP(), []int{0, 1}
}

func (x *PredictRequest_RequestOptions) GetClientId() []byte {
	if x != nil {
		return x.ClientId
	}
	return nil
}

func (x *PredictRequest_RequestOptions) GetDeterministicMode() PredictRequest_RequestOptions_DeterministicMode {
	if x != nil && x.DeterministicMode != nil {
		return *x.DeterministicMode
	}
	return PredictRequest_RequestOptions_DETERMINISTIC_MODE_UNSPECIFIED
}

func (x *PredictRequest_RequestOptions) GetReturnAdditionalArraysFromPrefill() bool {
	if x != nil && x.ReturnAdditionalArraysFromPrefill != nil {
		return *x.ReturnAdditionalArraysFromPrefill
	}
	return false
}

func (x *PredictRequest_RequestOptions) GetReturnStoptokens() []int64 {
	if x != nil {
		return x.ReturnStoptokens
	}
	return nil
}

var File_tensorflow_serving_predict_proto protoreflect.FileDescriptor

const file_tensorflow_serving_predict_proto_rawDesc = "" +
	"\n" +
	" tensorflow_serving/predict.proto\x12\x12tensorflow.serving\x1a&tensorflow/core/framework/tensor.proto\x1a\x1etensorflow_serving/model.proto\"\xf2\a\n" +
	"\x0ePredictRequest\x12<\n" +
	"\n" +
	"model_spec\x18\x01 \x01(\v2\x1d.tensorflow.serving.ModelSpecR\tmodelSpec\x12F\n" +
	"\x06inputs\x18\x02 \x03(\v2..tensorflow.serving.PredictRequest.InputsEntryR\x06inputs\x12#\n" +
	"\routput_filter\x18\x03 \x03(\tR\foutputFilter\x12d\n" +
	"\x18predict_streamed_options\x18\x05 \x01(\v2*.tensorflow.serving.PredictStreamedOptionsR\x16predictStreamedOptions\x12 \n" +
	"\tclient_id\x18\x06 \x01(\fH\x00R\bclientId\x88\x01\x01\x12_\n" +
	"\x0frequest_options\x18\a \x01(\v21.tensorflow.serving.PredictRequest.RequestOptionsH\x01R\x0erequestOptions\x88\x01\x01\x1aR\n" +
	"\vInputsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12-\n" +
	"\x05value\x18\x02 \x01(\v2\x17.tensorflow.TensorProtoR\x05value:\x028\x01\x1a\xcf\x03\n" +
	"\x0eRequestOptions\x12 \n" +
	"\tclient_id\x18\x01 \x01(\fH\x00R\bclientId\x88\x01\x01\x12w\n" +
	"\x12deterministic_mode\x18\x02 \x01(\x0e2C.tensorflow.serving.PredictRequest.RequestOptions.DeterministicModeH\x01R\x11deterministicMode\x88\x01\x01\x12U\n" +
	"%return_additional_arrays_from_prefill\x18\x03 \x01(\bH\x02R!returnAdditionalArraysFromPrefill\x88\x01\x01\x12+\n" +
	"\x11return_stoptokens\x18\x04 \x03(\x03R\x10returnStoptokens\"O\n" +
	"\x11DeterministicMode\x12\"\n" +
	"\x1eDETERMINISTIC_MODE_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12FIXED_DECODER_SLOT\x10\x01B\f\n" +
	"\n" +
	"_client_idB\x15\n" +
	"\x13_deterministic_modeB(\n" +
	"&_return_additional_arrays_from_prefillB\f\n" +
	"\n" +
	"_client_idB\x12\n" +
	"\x10_request_optionsJ\x04\b\x04\x10\x05\"\x9c\x03\n" +
	"\x16PredictStreamedOptions\x12\\\n" +
	"\rrequest_state\x18\x01 \x01(\x0e27.tensorflow.serving.PredictStreamedOptions.RequestStateR\frequestState\x12j\n" +
	"\x10split_dimensions\x18\x02 \x03(\v2?.tensorflow.serving.PredictStreamedOptions.SplitDimensionsEntryR\x0fsplitDimensions\x124\n" +
	"\x16return_single_response\x18\x03 \x01(\bR\x14returnSingleResponse\x1aB\n" +
	"\x14SplitDimensionsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\">\n" +
	"\fRequestState\x12\b\n" +
	"\x04NONE\x10\x00\x12\t\n" +
	"\x05SPLIT\x10\x01\x12\r\n" +
	"\tEND_SPLIT\x10\x02\x12\n" +
	"\n" +
	"\x06CANCEL\x10\x03\"\xf0\x01\n" +
	"\x0fPredictResponse\x12<\n" +
	"\n" +
	"model_spec\x18\x02 \x01(\v2\x1d.tensorflow.serving.ModelSpecR\tmodelSpec\x12J\n" +
	"\aoutputs\x18\x01 \x03(\v20.tensorflow.serving.PredictResponse.OutputsEntryR\aoutputs\x1aS\n" +
	"\fOutputsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12-\n" +
	"\x05value\x18\x02 \x01(\v2\x17.tensorflow.TensorProtoR\x05value:\x028\x01BAZ<rtb_model_server/proto/tensorflow_serving;tensorflow_serving\xf8\x01\x01b\x06proto3"

var (
	file_tensorflow_serving_predict_proto_rawDescOnce sync.Once
	file_tensorflow_serving_predict_proto_rawDescData []byte
)

func file_tensorflow_serving_predict_proto_rawDescGZIP() []byte {
	file_tensorflow_serving_predict_proto_rawDescOnce.Do(func() {
		file_tensorflow_serving_predict_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_tensorflow_serving_predict_proto_rawDesc), len(file_tensorflow_serving_predict_proto_rawDesc)))
	})
	return file_tensorflow_serving_predict_proto_rawDescData
}

var file_tensorflow_serving_predict_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_tensorflow_serving_predict_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_tensorflow_serving_predict_proto_goTypes = []any{
	(PredictRequest_RequestOptions_DeterministicMode)(0), // 0: tensorflow.serving.PredictRequest.RequestOptions.DeterministicMode
	(PredictStreamedOptions_RequestState)(0),             // 1: tensorflow.serving.PredictStreamedOptions.RequestState
	(*PredictRequest)(nil),                               // 2: tensorflow.serving.PredictRequest
	(*PredictStreamedOptions)(nil),                       // 3: tensorflow.serving.PredictStreamedOptions
	(*PredictResponse)(nil),                              // 4: tensorflow.serving.PredictResponse
	nil,                                                  // 5: tensorflow.serving.PredictRequest.InputsEntry
	(*PredictRequest_RequestOptions)(nil),                // 6: tensorflow.serving.PredictRequest.RequestOptions
	nil,                                                  // 7: tensorflow.serving.PredictStreamedOptions.SplitDimensionsEntry
	nil,                                                  // 8: tensorflow.serving.PredictResponse.OutputsEntry
	(*ModelSpec)(nil),                                    // 9: tensorflow.serving.ModelSpec
	(*framework.TensorProto)(nil),                        // 10: tensorflow.TensorProto
}
var file_tensorflow_serving_predict_proto_depIdxs = []int32{
	9,  // 0: tensorflow.serving.PredictRequest.model_spec:type_name -> tensorflow.serving.ModelSpec
	5,  // 1: tensorflow.serving.PredictRequest.inputs:type_name -> tensorflow.serving.PredictRequest.InputsEntry
	3,  // 2: tensorflow.serving.PredictRequest.predict_streamed_options:type_name -> tensorflow.serving.PredictStreamedOptions
	6,  // 3: tensorflow.serving.PredictRequest.request_options:type_name -> tensorflow.serving.PredictRequest.RequestOptions
	1,  // 4: tensorflow.serving.PredictStreamedOptions.request_state:type_name -> tensorflow.serving.PredictStreamedOptions.RequestState
	7,  // 5: tensorflow.serving.PredictStreamedOptions.split_dimensions:type_name -> tensorflow.serving.PredictStreamedOptions.SplitDimensionsEntry
	9,  // 6: tensorflow.serving.PredictResponse.model_spec:type_name -> tensorflow.serving.ModelSpec
	8,  // 7: tensorflow.serving.PredictResponse.outputs:type_name -> tensorflow.serving.PredictResponse.OutputsEntry
	10, // 8: tensorflow.serving.PredictRequest.InputsEntry.value:type_name -> tensorflow.TensorProto
	0,  // 9: tensorflow.serving.PredictRequest.RequestOptions.deterministic_mode:type_name -> tensorflow.serving.PredictRequest.RequestOptions.DeterministicMode
	10, // 10: tensorflow.serving.PredictResponse.OutputsEntry.value:type_name -> tensorflow.TensorProto
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_tensorflow_serving_predict_proto_init() }
func file_tensorflow_serving_predict_proto_init() {
	if File_tensorflow_serving_predict_proto != nil {
		return
	}
	file_tensorflow_serving_model_proto_init()
	file_tensorflow_serving_predict_proto_msgTypes[0].OneofWrappers = []any{}
	file_tensorflow_serving_predict_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_tensorflow_serving_predict_proto_rawDesc), len(file_tensorflow_serving_predict_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_serving_predict_proto_goTypes,
		DependencyIndexes: file_tensorflow_serving_predict_proto_depIdxs,
		EnumInfos:         file_tensorflow_serving_predict_proto_enumTypes,
		MessageInfos:      file_tensorflow_serving_predict_proto_msgTypes,
	}.Build()
	File_tensorflow_serving_predict_proto = out.File
	file_tensorflow_serving_predict_proto_goTypes = nil
	file_tensorflow_serving_predict_proto_depIdxs = nil
}
