// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: tensorflow/core/framework/types.proto

package framework

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// (== suppress_warning documentation-presence ==)
// LINT.IfChange
type DataType int32

const (
	// Not a legal value for DataType. Used to indicate a DataType field
	// has not been set.
	DataType_DT_INVALID DataType = 0
	// Data types that all computation devices are expected to be
	// capable to support.
	DataType_DT_FLOAT         DataType = 1
	DataType_DT_DOUBLE        DataType = 2
	DataType_DT_INT32         DataType = 3
	DataType_DT_UINT8         DataType = 4
	DataType_DT_INT16         DataType = 5
	DataType_DT_INT8          DataType = 6
	DataType_DT_STRING        DataType = 7
	DataType_DT_COMPLEX64     DataType = 8 // Single-precision complex
	DataType_DT_INT64         DataType = 9
	DataType_DT_BOOL          DataType = 10
	DataType_DT_QINT8         DataType = 11 // Quantized int8
	DataType_DT_QUINT8        DataType = 12 // Quantized uint8
	DataType_DT_QINT32        DataType = 13 // Quantized int32
	DataType_DT_BFLOAT16      DataType = 14 // Float32 truncated to 16 bits.
	DataType_DT_QINT16        DataType = 15 // Quantized int16
	DataType_DT_QUINT16       DataType = 16 // Quantized uint16
	DataType_DT_UINT16        DataType = 17
	DataType_DT_COMPLEX128    DataType = 18 // Double-precision complex
	DataType_DT_HALF          DataType = 19
	DataType_DT_RESOURCE      DataType = 20
	DataType_DT_VARIANT       DataType = 21 // Arbitrary C++ data types
	DataType_DT_UINT32        DataType = 22
	DataType_DT_UINT64        DataType = 23
	DataType_DT_FLOAT8_E5M2   DataType = 24 // 5 exponent bits, 2 mantissa bits.
	DataType_DT_FLOAT8_E4M3FN DataType = 25 // 4 exponent bits, 3 mantissa bits, finite-only, with
	// 2 NaNs (0bS1111111).
	DataType_DT_FLOAT8_E4M3FNUZ DataType = 26 // 4 exponent bits, 3 mantissa bits, finite-only,
	// with NaN.
	DataType_DT_FLOAT8_E4M3B11FNUZ DataType = 27 // 4 exponent bits, 3 mantissa bits, 11 bits
	// bias, finite-only, with NaNs.
	DataType_DT_FLOAT8_E5M2FNUZ DataType = 28 // 5 exponent bits, 2 mantissa bits, finite-only,
	// with NaN.
	DataType_DT_INT4  DataType = 29
	DataType_DT_UINT4 DataType = 30
	DataType_DT_INT2  DataType = 31
	DataType_DT_UINT2 DataType = 32
	// Do not use! These are only for TF1's obsolete reference Variables.
	// Every enum above should have a corresponding value below (verified by
	// types_test).
	DataType_DT_FLOAT_REF              DataType = 101
	DataType_DT_DOUBLE_REF             DataType = 102
	DataType_DT_INT32_REF              DataType = 103
	DataType_DT_UINT8_REF              DataType = 104
	DataType_DT_INT16_REF              DataType = 105
	DataType_DT_INT8_REF               DataType = 106
	DataType_DT_STRING_REF             DataType = 107
	DataType_DT_COMPLEX64_REF          DataType = 108
	DataType_DT_INT64_REF              DataType = 109
	DataType_DT_BOOL_REF               DataType = 110
	DataType_DT_QINT8_REF              DataType = 111
	DataType_DT_QUINT8_REF             DataType = 112
	DataType_DT_QINT32_REF             DataType = 113
	DataType_DT_BFLOAT16_REF           DataType = 114
	DataType_DT_QINT16_REF             DataType = 115
	DataType_DT_QUINT16_REF            DataType = 116
	DataType_DT_UINT16_REF             DataType = 117
	DataType_DT_COMPLEX128_REF         DataType = 118
	DataType_DT_HALF_REF               DataType = 119
	DataType_DT_RESOURCE_REF           DataType = 120
	DataType_DT_VARIANT_REF            DataType = 121
	DataType_DT_UINT32_REF             DataType = 122
	DataType_DT_UINT64_REF             DataType = 123
	DataType_DT_FLOAT8_E5M2_REF        DataType = 124
	DataType_DT_FLOAT8_E4M3FN_REF      DataType = 125
	DataType_DT_FLOAT8_E4M3FNUZ_REF    DataType = 126
	DataType_DT_FLOAT8_E4M3B11FNUZ_REF DataType = 127
	DataType_DT_FLOAT8_E5M2FNUZ_REF    DataType = 128
	DataType_DT_INT4_REF               DataType = 129
	DataType_DT_UINT4_REF              DataType = 130
	DataType_DT_INT2_REF               DataType = 131
	DataType_DT_UINT2_REF              DataType = 132
)

// Enum value maps for DataType.
var (
	DataType_name = map[int32]string{
		0:   "DT_INVALID",
		1:   "DT_FLOAT",
		2:   "DT_DOUBLE",
		3:   "DT_INT32",
		4:   "DT_UINT8",
		5:   "DT_INT16",
		6:   "DT_INT8",
		7:   "DT_STRING",
		8:   "DT_COMPLEX64",
		9:   "DT_INT64",
		10:  "DT_BOOL",
		11:  "DT_QINT8",
		12:  "DT_QUINT8",
		13:  "DT_QINT32",
		14:  "DT_BFLOAT16",
		15:  "DT_QINT16",
		16:  "DT_QUINT16",
		17:  "DT_UINT16",
		18:  "DT_COMPLEX128",
		19:  "DT_HALF",
		20:  "DT_RESOURCE",
		21:  "DT_VARIANT",
		22:  "DT_UINT32",
		23:  "DT_UINT64",
		24:  "DT_FLOAT8_E5M2",
		25:  "DT_FLOAT8_E4M3FN",
		26:  "DT_FLOAT8_E4M3FNUZ",
		27:  "DT_FLOAT8_E4M3B11FNUZ",
		28:  "DT_FLOAT8_E5M2FNUZ",
		29:  "DT_INT4",
		30:  "DT_UINT4",
		31:  "DT_INT2",
		32:  "DT_UINT2",
		101: "DT_FLOAT_REF",
		102: "DT_DOUBLE_REF",
		103: "DT_INT32_REF",
		104: "DT_UINT8_REF",
		105: "DT_INT16_REF",
		106: "DT_INT8_REF",
		107: "DT_STRING_REF",
		108: "DT_COMPLEX64_REF",
		109: "DT_INT64_REF",
		110: "DT_BOOL_REF",
		111: "DT_QINT8_REF",
		112: "DT_QUINT8_REF",
		113: "DT_QINT32_REF",
		114: "DT_BFLOAT16_REF",
		115: "DT_QINT16_REF",
		116: "DT_QUINT16_REF",
		117: "DT_UINT16_REF",
		118: "DT_COMPLEX128_REF",
		119: "DT_HALF_REF",
		120: "DT_RESOURCE_REF",
		121: "DT_VARIANT_REF",
		122: "DT_UINT32_REF",
		123: "DT_UINT64_REF",
		124: "DT_FLOAT8_E5M2_REF",
		125: "DT_FLOAT8_E4M3FN_REF",
		126: "DT_FLOAT8_E4M3FNUZ_REF",
		127: "DT_FLOAT8_E4M3B11FNUZ_REF",
		128: "DT_FLOAT8_E5M2FNUZ_REF",
		129: "DT_INT4_REF",
		130: "DT_UINT4_REF",
		131: "DT_INT2_REF",
		132: "DT_UINT2_REF",
	}
	DataType_value = map[string]int32{
		"DT_INVALID":                0,
		"DT_FLOAT":                  1,
		"DT_DOUBLE":                 2,
		"DT_INT32":                  3,
		"DT_UINT8":                  4,
		"DT_INT16":                  5,
		"DT_INT8":                   6,
		"DT_STRING":                 7,
		"DT_COMPLEX64":              8,
		"DT_INT64":                  9,
		"DT_BOOL":                   10,
		"DT_QINT8":                  11,
		"DT_QUINT8":                 12,
		"DT_QINT32":                 13,
		"DT_BFLOAT16":               14,
		"DT_QINT16":                 15,
		"DT_QUINT16":                16,
		"DT_UINT16":                 17,
		"DT_COMPLEX128":             18,
		"DT_HALF":                   19,
		"DT_RESOURCE":               20,
		"DT_VARIANT":                21,
		"DT_UINT32":                 22,
		"DT_UINT64":                 23,
		"DT_FLOAT8_E5M2":            24,
		"DT_FLOAT8_E4M3FN":          25,
		"DT_FLOAT8_E4M3FNUZ":        26,
		"DT_FLOAT8_E4M3B11FNUZ":     27,
		"DT_FLOAT8_E5M2FNUZ":        28,
		"DT_INT4":                   29,
		"DT_UINT4":                  30,
		"DT_INT2":                   31,
		"DT_UINT2":                  32,
		"DT_FLOAT_REF":              101,
		"DT_DOUBLE_REF":             102,
		"DT_INT32_REF":              103,
		"DT_UINT8_REF":              104,
		"DT_INT16_REF":              105,
		"DT_INT8_REF":               106,
		"DT_STRING_REF":             107,
		"DT_COMPLEX64_REF":          108,
		"DT_INT64_REF":              109,
		"DT_BOOL_REF":               110,
		"DT_QINT8_REF":              111,
		"DT_QUINT8_REF":             112,
		"DT_QINT32_REF":             113,
		"DT_BFLOAT16_REF":           114,
		"DT_QINT16_REF":             115,
		"DT_QUINT16_REF":            116,
		"DT_UINT16_REF":             117,
		"DT_COMPLEX128_REF":         118,
		"DT_HALF_REF":               119,
		"DT_RESOURCE_REF":           120,
		"DT_VARIANT_REF":            121,
		"DT_UINT32_REF":             122,
		"DT_UINT64_REF":             123,
		"DT_FLOAT8_E5M2_REF":        124,
		"DT_FLOAT8_E4M3FN_REF":      125,
		"DT_FLOAT8_E4M3FNUZ_REF":    126,
		"DT_FLOAT8_E4M3B11FNUZ_REF": 127,
		"DT_FLOAT8_E5M2FNUZ_REF":    128,
		"DT_INT4_REF":               129,
		"DT_UINT4_REF":              130,
		"DT_INT2_REF":               131,
		"DT_UINT2_REF":              132,
	}
)

func (x DataType) Enum() *DataType {
	p := new(DataType)
	*p = x
	return p
}

func (x DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_tensorflow_core_framework_types_proto_enumTypes[0].Descriptor()
}

func (DataType) Type() protoreflect.EnumType {
	return &file_tensorflow_core_framework_types_proto_enumTypes[0]
}

func (x DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataType.Descriptor instead.
func (DataType) EnumDescriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_types_proto_rawDescGZIP(), []int{0}
}

// Represents a serialized tf.dtypes.Dtype
type SerializedDType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Datatype      DataType               `protobuf:"varint,1,opt,name=datatype,proto3,enum=tensorflow.DataType" json:"datatype,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SerializedDType) Reset() {
	*x = SerializedDType{}
	mi := &file_tensorflow_core_framework_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SerializedDType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SerializedDType) ProtoMessage() {}

func (x *SerializedDType) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SerializedDType.ProtoReflect.Descriptor instead.
func (*SerializedDType) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_types_proto_rawDescGZIP(), []int{0}
}

func (x *SerializedDType) GetDatatype() DataType {
	if x != nil {
		return x.Datatype
	}
	return DataType_DT_INVALID
}

var File_tensorflow_core_framework_types_proto protoreflect.FileDescriptor

const file_tensorflow_core_framework_types_proto_rawDesc = "" +
	"\n" +
	"%tensorflow/core/framework/types.proto\x12\n" +
	"tensorflow\"C\n" +
	"\x0fSerializedDType\x120\n" +
	"\bdatatype\x18\x01 \x01(\x0e2\x14.tensorflow.DataTypeR\bdatatype*\xa9\t\n" +
	"\bDataType\x12\x0e\n" +
	"\n" +
	"DT_INVALID\x10\x00\x12\f\n" +
	"\bDT_FLOAT\x10\x01\x12\r\n" +
	"\tDT_DOUBLE\x10\x02\x12\f\n" +
	"\bDT_INT32\x10\x03\x12\f\n" +
	"\bDT_UINT8\x10\x04\x12\f\n" +
	"\bDT_INT16\x10\x05\x12\v\n" +
	"\aDT_INT8\x10\x06\x12\r\n" +
	"\tDT_STRING\x10\a\x12\x10\n" +
	"\fDT_COMPLEX64\x10\b\x12\f\n" +
	"\bDT_INT64\x10\t\x12\v\n" +
	"\aDT_BOOL\x10\n" +
	"\x12\f\n" +
	"\bDT_QINT8\x10\v\x12\r\n" +
	"\tDT_QUINT8\x10\f\x12\r\n" +
	"\tDT_QINT32\x10\r\x12\x0f\n" +
	"\vDT_BFLOAT16\x10\x0e\x12\r\n" +
	"\tDT_QINT16\x10\x0f\x12\x0e\n" +
	"\n" +
	"DT_QUINT16\x10\x10\x12\r\n" +
	"\tDT_UINT16\x10\x11\x12\x11\n" +
	"\rDT_COMPLEX128\x10\x12\x12\v\n" +
	"\aDT_HALF\x10\x13\x12\x0f\n" +
	"\vDT_RESOURCE\x10\x14\x12\x0e\n" +
	"\n" +
	"DT_VARIANT\x10\x15\x12\r\n" +
	"\tDT_UINT32\x10\x16\x12\r\n" +
	"\tDT_UINT64\x10\x17\x12\x12\n" +
	"\x0eDT_FLOAT8_E5M2\x10\x18\x12\x14\n" +
	"\x10DT_FLOAT8_E4M3FN\x10\x19\x12\x16\n" +
	"\x12DT_FLOAT8_E4M3FNUZ\x10\x1a\x12\x19\n" +
	"\x15DT_FLOAT8_E4M3B11FNUZ\x10\x1b\x12\x16\n" +
	"\x12DT_FLOAT8_E5M2FNUZ\x10\x1c\x12\v\n" +
	"\aDT_INT4\x10\x1d\x12\f\n" +
	"\bDT_UINT4\x10\x1e\x12\v\n" +
	"\aDT_INT2\x10\x1f\x12\f\n" +
	"\bDT_UINT2\x10 \x12\x10\n" +
	"\fDT_FLOAT_REF\x10e\x12\x11\n" +
	"\rDT_DOUBLE_REF\x10f\x12\x10\n" +
	"\fDT_INT32_REF\x10g\x12\x10\n" +
	"\fDT_UINT8_REF\x10h\x12\x10\n" +
	"\fDT_INT16_REF\x10i\x12\x0f\n" +
	"\vDT_INT8_REF\x10j\x12\x11\n" +
	"\rDT_STRING_REF\x10k\x12\x14\n" +
	"\x10DT_COMPLEX64_REF\x10l\x12\x10\n" +
	"\fDT_INT64_REF\x10m\x12\x0f\n" +
	"\vDT_BOOL_REF\x10n\x12\x10\n" +
	"\fDT_QINT8_REF\x10o\x12\x11\n" +
	"\rDT_QUINT8_REF\x10p\x12\x11\n" +
	"\rDT_QINT32_REF\x10q\x12\x13\n" +
	"\x0fDT_BFLOAT16_REF\x10r\x12\x11\n" +
	"\rDT_QINT16_REF\x10s\x12\x12\n" +
	"\x0eDT_QUINT16_REF\x10t\x12\x11\n" +
	"\rDT_UINT16_REF\x10u\x12\x15\n" +
	"\x11DT_COMPLEX128_REF\x10v\x12\x0f\n" +
	"\vDT_HALF_REF\x10w\x12\x13\n" +
	"\x0fDT_RESOURCE_REF\x10x\x12\x12\n" +
	"\x0eDT_VARIANT_REF\x10y\x12\x11\n" +
	"\rDT_UINT32_REF\x10z\x12\x11\n" +
	"\rDT_UINT64_REF\x10{\x12\x16\n" +
	"\x12DT_FLOAT8_E5M2_REF\x10|\x12\x18\n" +
	"\x14DT_FLOAT8_E4M3FN_REF\x10}\x12\x1a\n" +
	"\x16DT_FLOAT8_E4M3FNUZ_REF\x10~\x12\x1d\n" +
	"\x19DT_FLOAT8_E4M3B11FNUZ_REF\x10\x7f\x12\x1b\n" +
	"\x16DT_FLOAT8_E5M2FNUZ_REF\x10\x80\x01\x12\x10\n" +
	"\vDT_INT4_REF\x10\x81\x01\x12\x11\n" +
	"\fDT_UINT4_REF\x10\x82\x01\x12\x10\n" +
	"\vDT_INT2_REF\x10\x83\x01\x12\x11\n" +
	"\fDT_UINT2_REF\x10\x84\x01B^\n" +
	"\x18org.tensorflow.frameworkB\vTypesProtosP\x01Z0rtb_model_server/proto/tensorflow/core/framework\xf8\x01\x01b\x06proto3"

var (
	file_tensorflow_core_framework_types_proto_rawDescOnce sync.Once
	file_tensorflow_core_framework_types_proto_rawDescData []byte
)

func file_tensorflow_core_framework_types_proto_rawDescGZIP() []byte {
	file_tensorflow_core_framework_types_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_framework_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_tensorflow_core_framework_types_proto_rawDesc), len(file_tensorflow_core_framework_types_proto_rawDesc)))
	})
	return file_tensorflow_core_framework_types_proto_rawDescData
}

var file_tensorflow_core_framework_types_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_tensorflow_core_framework_types_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_tensorflow_core_framework_types_proto_goTypes = []any{
	(DataType)(0),           // 0: tensorflow.DataType
	(*SerializedDType)(nil), // 1: tensorflow.SerializedDType
}
var file_tensorflow_core_framework_types_proto_depIdxs = []int32{
	0, // 0: tensorflow.SerializedDType.datatype:type_name -> tensorflow.DataType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tensorflow_core_framework_types_proto_init() }
func file_tensorflow_core_framework_types_proto_init() {
	if File_tensorflow_core_framework_types_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_tensorflow_core_framework_types_proto_rawDesc), len(file_tensorflow_core_framework_types_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_framework_types_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_framework_types_proto_depIdxs,
		EnumInfos:         file_tensorflow_core_framework_types_proto_enumTypes,
		MessageInfos:      file_tensorflow_core_framework_types_proto_msgTypes,
	}.Build()
	File_tensorflow_core_framework_types_proto = out.File
	file_tensorflow_core_framework_types_proto_goTypes = nil
	file_tensorflow_core_framework_types_proto_depIdxs = nil
}
