# AdIndexManager VERSION文件监控方案迁移

## 概述

本次修改将 `AdIndexManager` 的 VERSION 文件更新检测机制从基于 `fsnotify` 的事件通知改为轮询方案，以提供更好的兼容性。

## 主要变更

### 1. 配置文件修改

#### conf/conf.go
- 在 `ModelIndexConfig` 结构体中新增 `VersionFilePollInterval` 字段
- 用于配置 VERSION 文件的轮询间隔（秒）

#### conf/rtb_model_server.yaml
- 新增配置项：`VersionFilePollInterval: 30`
- 默认轮询间隔为 30 秒

### 2. AdIndexManager 结构体修改

#### 移除的字段
- `watcher *fsnotify.Watcher` - 文件系统监控器

#### 新增的字段
- `pollTicker *time.Ticker` - 轮询定时器

### 3. 方法修改

#### StartFileWatcher()
- 从基于 `fsnotify` 的文件监控改为基于 `time.Ticker` 的轮询
- 支持可配置的轮询间隔
- 默认轮询间隔为 30 秒

#### StopFileWatcher()
- 停止轮询定时器而不是文件监控器

#### pollVersionFile() (原 watchVersionFile())
- 使用定时器触发版本检查
- 通过读取文件内容比较版本变化
- 检测到变化时触发重新加载

### 4. 依赖变更

#### 移除的依赖
- `github.com/fsnotify/fsnotify`

## 优势

1. **更好的兼容性**：轮询方案不依赖文件系统事件，在各种环境下都能稳定工作
2. **简化依赖**：移除了对 `fsnotify` 的依赖
3. **可配置性**：轮询间隔可通过配置文件调整
4. **稳定性**：避免了文件系统事件可能丢失的问题

## 配置说明

在 `conf/rtb_model_server.yaml` 中配置轮询间隔：

```yaml
ModelIndex:
  # ... 其他配置 ...
  VersionFilePollInterval: 30  # VERSION文件轮询间隔（秒），默认30秒
```

## 注意事项

1. 轮询间隔不宜设置过小，以避免频繁的文件 I/O 操作
2. 轮询间隔也不宜设置过大，以确保能及时检测到版本变化
3. 推荐的轮询间隔范围：10-60 秒

## 向后兼容性

- 如果配置文件中未设置 `VersionFilePollInterval`，将使用默认值 30 秒
- 现有的重新加载逻辑保持不变
- API 接口保持兼容