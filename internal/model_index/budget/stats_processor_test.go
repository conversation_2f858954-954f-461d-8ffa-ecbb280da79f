package budget

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"testing"
	"time"

	"rtb_model_server/conf"
)

// TestPollingFunctionality 测试轮询功能
func TestPollingFunctionality(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "stats_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 设置测试配置
	conf.GlobalConfig.BudgetConfig.StatsFileDir = tempDir
	conf.GlobalConfig.BudgetConfig.StatsFileNamePattern = "test_stats"
	conf.GlobalConfig.BudgetConfig.PollingInterval = 1 // 1秒轮询间隔

	// 创建测试统计文件
	testFile := filepath.Join(tempDir, "test_stats.20240115143000")
	// 格式：uid\tpid\tsid\tcid\tbid\twin\timp\tclk\tcost\tbudget\tspend
	testContent := "user1\t100\t10\t1001\t1000\t1\t1\t0\t1000000\t10000000\t500000\n" +
				 "user2\t200\t20\t1002\t2000\t1\t1\t0\t2000000\t20000000\t1000000\n"
	err = os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 创建新的StatsProcessor实例
	statsProcessor := &StatsProcessor{
		statsMap:         make(map[int32]*BudgetStats),
		strategyStatsMap: make(map[int32]*Stats),
		campaignStatsMap: make(map[int32]*Stats),
		lastLoadTime:     time.Now(),
		stopChan:         make(chan struct{}),
	}

	// 初始化文件名匹配模式
	fileNamePattern := conf.GlobalConfig.BudgetConfig.StatsFileNamePattern
	if fileNamePattern != "" {
		pattern := fmt.Sprintf(`^%s\.\d{14}$`, regexp.QuoteMeta(fileNamePattern))
		regex, err := regexp.Compile(pattern)
		if err != nil {
			t.Fatalf("Failed to compile regex pattern: %v", err)
		}
		statsProcessor.fileNamePattern = regex
	}

	// 手动加载一次数据来验证功能
	t.Logf("Attempting to load stats from dir: %s", tempDir)
	t.Logf("Pattern: %s", conf.GlobalConfig.BudgetConfig.StatsFileNamePattern)
	t.Logf("Test file created: %s", testFile)
	
	// 检查文件是否存在
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Fatalf("Test file does not exist: %s", testFile)
	}
	
	err = statsProcessor.LoadStats()
	if err != nil {
		t.Fatalf("Failed to load stats: %v", err)
	}

	// 验证数据是否加载
	statsProcessor.mu.RLock()
	if len(statsProcessor.statsMap) == 0 {
		t.Errorf("Stats not loaded. Current file: %s", statsProcessor.currentStatsFile)
		// 尝试直接调用getLatestStatsFile来调试
		latestFile, err := statsProcessor.getLatestStatsFile()
		if err != nil {
			t.Errorf("getLatestStatsFile error: %v", err)
		} else {
			t.Errorf("getLatestStatsFile returned: %s", latestFile)
		}
	} else {
		t.Logf("Successfully loaded %d stats entries", len(statsProcessor.statsMap))
	}
	statsProcessor.mu.RUnlock()

	// 测试轮询初始化
	err = statsProcessor.initPolling()
	if err != nil {
		t.Fatalf("Failed to init polling: %v", err)
	}

	// 停止轮询
	statsProcessor.StopPolling()

	t.Log("Polling functionality test passed")
}

// TestPollingInterval 测试轮询间隔配置
func TestPollingInterval(t *testing.T) {
	// 测试默认轮询间隔
	conf.GlobalConfig.BudgetConfig.PollingInterval = 0
	statsProcessor := &StatsProcessor{
		statsMap:         make(map[int32]*BudgetStats),
		strategyStatsMap: make(map[int32]*Stats),
		campaignStatsMap: make(map[int32]*Stats),
		lastLoadTime:     time.Now(),
		stopChan:         make(chan struct{}),
	}

	err := statsProcessor.initPolling()
	if err != nil {
		t.Fatalf("Failed to init polling: %v", err)
	}

	// 验证默认间隔为30秒
	if statsProcessor.pollingInterval != 30*time.Second {
		t.Errorf("Expected default polling interval 30s, got %v", statsProcessor.pollingInterval)
	}

	statsProcessor.StopPolling()

	// 测试自定义轮询间隔
	conf.GlobalConfig.BudgetConfig.PollingInterval = 60
	statsProcessor2 := &StatsProcessor{
		statsMap:         make(map[int32]*BudgetStats),
		strategyStatsMap: make(map[int32]*Stats),
		campaignStatsMap: make(map[int32]*Stats),
		lastLoadTime:     time.Now(),
		stopChan:         make(chan struct{}),
	}

	err = statsProcessor2.initPolling()
	if err != nil {
		t.Fatalf("Failed to init polling: %v", err)
	}

	// 验证自定义间隔为60秒
	if statsProcessor2.pollingInterval != 60*time.Second {
		t.Errorf("Expected custom polling interval 60s, got %v", statsProcessor2.pollingInterval)
	}

	statsProcessor2.StopPolling()

	t.Log("Polling interval test passed")
}