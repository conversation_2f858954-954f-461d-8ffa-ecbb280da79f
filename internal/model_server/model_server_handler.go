package model_server

import (
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/internal/kafka"
	"rtb_model_server/internal/zaplog"
	"time"

	"rtb_model_server/common/domob_thrift/rtb_model_server"
	funnel "rtb_model_server/common/funnel_gengo"

	up "rtb_model_server/internal/user_profile"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type ModelServerHandler struct {
}

// Parameters:
//   - BidRequest
//   - AdMap
func (s ModelServerHandler) GetAdBid(bid_request *rtb_types.RTBBidRequest, ad_map map[rtb_types.AdPlacementId][]*rtb_model_server.RTBModelServerAdInfo) (r *rtb_model_server.RTBModelServerResponse, err error) {
	return nil, nil
}

// Parameters:
//   - BidRequest
func (s ModelServerHandler) GetAd(req *rtb_types.RTBBidRequest) (r *rtb_model_server.RTBModelServerResponse, err error) {
	zaplog.Logger.Info("Entering GetAd function")

	t1 := time.Now()
	resp := &rtb_model_server.RTBModelServerResponse{}
	gModelIndexManager := GetGlobalModelIndexManger()

	runtimeCtx := gModelIndexManager.CreateContext(req, resp)
	zaplog.Logger.Info("Created runtime context")

	defer func() {
		runtimeCtx.AddTimeCost("total", time.Since(t1).Microseconds())
		zaplog.Logger.Info("request", zap.String("ctx", runtimeCtx.String()))
		if runtimeCtx.EnableFunnelLog {
			// 记录漏斗日志
			funnelData := runtimeCtx.FullLog()
			zaplog.FunnelLogger.Info("funnel log", zap.Any("data", funnelData))

			// 通过Kafka发送漏斗数据
			if producer := kafka.GetFunnelProducer(); producer != nil {
				// 类型断言将 interface{} 转换为 *funnel.FunnelData
				if funnelDataPtr, ok := funnelData.(*funnel.FunnelData); ok {
					if err := producer.SendFunnelDataAsync(funnelDataPtr); err != nil {
						zaplog.Logger.Error("Failed to send funnel data to Kafka", zap.Error(err))
					}
				} else {
					zaplog.Logger.Error("Failed to convert funnel data to *funnel.FunnelData")
				}
			}
		}
	}()

	// 执行用户画像填充

	err = up.GetUserProfileProcessor().Process(runtimeCtx)
	runtimeCtx.AddTimeCost("up", time.Since(t1).Microseconds())
	if err != nil {
		return nil, errors.Wrap(err, "fill user profile error")
	}

	// 打印日志
	zaplog.Logger.Info("User profile filled")

	// 执行广告索引预过滤
	preRecallCreatives := gModelIndexManager.PreFilter(runtimeCtx)
	zaplog.Logger.Info("Pre-filtered creatives")

	// 执行预算控制
	budgetControlResult, err := gModelIndexManager.BudgetControl(runtimeCtx, preRecallCreatives)
	// 打印日志
	zaplog.Logger.Info("Budget control passed")
	if err != nil {
		return nil, errors.Wrap(err, "budget control error")
	}

	// 执行预估
	predictResults, err := gModelIndexManager.Predict(runtimeCtx, budgetControlResult)
	zaplog.Logger.Info("Predict results")
	if err != nil {
		zaplog.Logger.Warn("failed to call predict", zap.Error(err))
		return nil, errors.Wrap(err, "predict error")
	}

	// 执行广告决策
	cidInfo, err := gModelIndexManager.BidDecision(runtimeCtx, predictResults)
	if err != nil {
		zaplog.Logger.Warn("failed to call bid decision", zap.Error(err))
		return nil, errors.Wrap(err, "bid decision error")
	}

	// 对于决策后的结果进行日志打印
	zaplog.Logger.Debug("BidDecision", zap.Any("BidResult", cidInfo))

	// 执行广告渲染
	resp, err = gModelIndexManager.Render(runtimeCtx, cidInfo)
	if err != nil {
		return nil, errors.Wrap(err, "render error")
	}
	zaplog.Logger.Debug("BidDecision", zap.Any("BidResult", cidInfo))
	return resp, nil
}

// Parameters:
//   - BidRequest
func (s ModelServerHandler) GetAdForDebug(bid_request *rtb_types.RTBBidRequest) (r *rtb_model_server.RTBModelServerResponseForDebug, err error) {
	return nil, nil
}
