package budget

import (
	"fmt"
	"sync"
	"time"

	"rtb_model_server/conf"
	"rtb_model_server/internal/context"
	"rtb_model_server/internal/model_index/filters"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// 预算控制处理器（单例）
type BudgetProcessor struct {
	statsProcessor  *StatsProcessor           // 统计处理器
	strategyMap     map[string]BudgetStrategy // 策略映射
	defaultStrategy BudgetStrategy            // 默认策略
	mu              sync.RWMutex              // 读写锁
}

var (
	budgetProcessorInstance *BudgetProcessor
	budgetProcessorOnce     sync.Once
)

// 获取预算处理器单例
func GetBudgetProcessor() *BudgetProcessor {
	budgetProcessorOnce.Do(func() {
		budgetProcessorInstance = &BudgetProcessor{
			statsProcessor:  GetStatsProcessor(),
			strategyMap:     make(map[string]BudgetStrategy),
			defaultStrategy: NewBaseBudgetStrategy(),
		}
		// 注册策略
		budgetProcessorInstance.registerStrategies()
	})
	return budgetProcessorInstance
}

// 注册预算控制策略
func (b *BudgetProcessor) registerStrategies() {
	b.strategyMap["base"] = NewBaseBudgetStrategy()
	b.strategyMap["cpc"] = NewCPCBudgetStrategy()
	b.strategyMap["awake"] = NewAwakeBudgetStrategy()
	b.strategyMap["random"] = NewRandomBudgetStrategy(0.3)
}

// 应用预算控制策略
func (b *BudgetProcessor) ApplyBudgetControl(ctx *context.RequestContext, cids []int32) map[int32]*BudgetControlResult {
	b.mu.RLock()
	defer b.mu.RUnlock()

	results := make(map[int32]*BudgetControlResult)

	for _, cid := range cids {
		// 获取创意信息以确定使用哪种策略
		creative, err := ctx.AdIndex.GetCreative(cid)
		if err != nil {
			results[cid] = &BudgetControlResult{
				Allowed: false,
				Reason:  "creative not found",
			}
			continue
		}

		// 获取策略信息
		strategy, err := ctx.AdIndex.GetStrategy(creative.StrategyId)
		if err != nil {
			results[cid] = &BudgetControlResult{
				Allowed: false,
				Reason:  "strategy not found",
			}
			continue
		}

		// 选择预算控制策略
		budgetStrategy := b.selectStrategy(int32(strategy.CostType), int32(creative.AdMatchType))

		// 应用预算控制
		result := budgetStrategy.ApplyBudgetControl(ctx, cid)
		results[cid] = result

		// 记录日志
		if !result.Allowed {
			zaplog.Logger.Debug("budget control rejected",
				zap.Int32("cid", cid),
				zap.String("strategy", budgetStrategy.GetStrategyName()),
				zap.String("reason", result.Reason))
		}
	}

	return results
}

// 选择预算控制策略
func (b *BudgetProcessor) selectStrategy(costType int32, adMatchType int32) BudgetStrategy {
	// FIXME 这里需要根据成本类型和广告匹配类型选择合适的策略

	// 默认策略
	return b.defaultStrategy
}

// 加载统计数据（委托给StatsProcessor）
func (b *BudgetProcessor) LoadStats() error {
	return b.statsProcessor.LoadStats()
}

// 预算过滤主入口（兼容旧接口）
func (b *BudgetProcessor) Process(ctx *context.RequestContext, creativeIds []int32) (map[int32]*BudgetControlResult, error) {
	// 定期重新加载统计数据
	config := &conf.GlobalConfig.BudgetConfig
	if time.Since(b.statsProcessor.lastLoadTime) > time.Duration(config.StatsReloadInterval)*time.Second {
		if err := b.LoadStats(); err != nil {
			zaplog.Logger.Error("failed to reload stats", zap.Error(err))
			return nil, err
		}
	}

	// 应用预算控制
	results := b.ApplyBudgetControl(ctx, creativeIds)

	// 设置过滤结果到过滤原因中
	for cid, result := range results {
		if !result.Allowed {
			ctx.CreativeFilterMap[cid] = int32(filters.FilterTypeBudgetReason) // 预算过滤
		}
	}

	return results, nil
}

// 更新预算消耗
func (b *BudgetProcessor) UpdateBudgetConsumption(creativeId int32, cost float64, impressions int64, clicks int64) {
	b.statsProcessor.UpdateStats(creativeId, cost, impressions, clicks)
}

// 获取统计信息
func (b *BudgetProcessor) GetStats(creativeId int32) *BudgetStats {
	return b.statsProcessor.GetStats(creativeId)
}

// 获取所有统计信息
func (b *BudgetProcessor) GetAllStats() map[int32]*BudgetStats {
	return b.statsProcessor.GetAllStats()
}

// 获取预算控制统计信息
func (b *BudgetProcessor) GetBudgetControlStats() map[string]interface{} {
	b.mu.RLock()
	defer b.mu.RUnlock()

	stats := b.statsProcessor.GetStatsSummary()
	stats["registered_strategies"] = len(b.strategyMap)

	// 添加策略列表
	strategyNames := make([]string, 0, len(b.strategyMap))
	for name := range b.strategyMap {
		strategyNames = append(strategyNames, name)
	}
	stats["strategy_names"] = strategyNames
	stats["default_strategy"] = b.defaultStrategy.GetStrategyName()

	return stats
}

// 添加自定义策略
func (b *BudgetProcessor) AddStrategy(name string, strategy BudgetStrategy) {
	b.mu.Lock()
	defer b.mu.Unlock()

	b.strategyMap[name] = strategy
	zaplog.Logger.Info("budget strategy added",
		zap.String("name", name),
		zap.String("strategy_type", strategy.GetStrategyName()))
}

// 移除策略
func (b *BudgetProcessor) RemoveStrategy(name string) {
	b.mu.Lock()
	defer b.mu.Unlock()

	delete(b.strategyMap, name)
	zaplog.Logger.Info("budget strategy removed", zap.String("name", name))
}

// 设置默认策略
func (b *BudgetProcessor) SetDefaultStrategy(strategy BudgetStrategy) {
	b.mu.Lock()
	defer b.mu.Unlock()

	b.defaultStrategy = strategy
	zaplog.Logger.Info("default budget strategy changed",
		zap.String("strategy_type", strategy.GetStrategyName()))
}

// 停止轮询监控
func (b *BudgetProcessor) StopPolling() {
	b.statsProcessor.StopPolling()
	zaplog.Logger.Info("budget processor polling stopped")
}

// 获取策略列表
func (b *BudgetProcessor) GetStrategies() map[string]BudgetStrategy {
	b.mu.RLock()
	defer b.mu.RUnlock()

	// 返回副本以避免并发问题
	result := make(map[string]BudgetStrategy)
	for k, v := range b.strategyMap {
		result[k] = v
	}
	return result
}

// 测试单个创意的预算控制
func (b *BudgetProcessor) TestBudgetControl(ctx *context.RequestContext, cid int32, strategyName string) *BudgetControlResult {
	b.mu.RLock()
	defer b.mu.RUnlock()

	var strategy BudgetStrategy
	if strategyName != "" {
		if s, exists := b.strategyMap[strategyName]; exists {
			strategy = s
		} else {
			return &BudgetControlResult{
				Allowed: false,
				Reason:  fmt.Sprintf("strategy '%s' not found", strategyName),
			}
		}
	} else {
		// 自动选择策略
		creative, err := ctx.AdIndex.GetCreative(cid)
		if err != nil {
			return &BudgetControlResult{
				Allowed: false,
				Reason:  "creative not found",
			}
		}

		strategyInfo, err := ctx.AdIndex.GetStrategy(creative.StrategyId)
		if err != nil {
			return &BudgetControlResult{
				Allowed: false,
				Reason:  "strategy not found",
			}
		}

		strategy = b.selectStrategy(int32(strategyInfo.CostType), int32(creative.AdMatchType))
	}

	return strategy.ApplyBudgetControl(ctx, cid)
}
