package up

import (
	"bufio"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"
	"strconv"
	"strings"
	"sync"

	"github.com/lionsoul2014/ip2region/binding/golang/xdb"
	"go.uber.org/zap"
)

// IP2RegionFiller IP地址到城市编码的填充器
type IP2RegionFiller struct {
	searcher    *xdb.Searcher
	enabled     bool
	cityCodeMap map[string]int // 城市名称到城市编码的映射，常驻内存
}

// 单例相关变量
var (
	ip2regionInstance *IP2RegionFiller
	ip2regionOnce     sync.Once
)

// NewIP2RegionFiller 创建IP2Region填充器（单例模式）
// 确保只有一个实例常驻内存，避免每次请求都重新加载数据库
func NewIP2RegionFiller() (*IP2RegionFiller, error) {
	var initErr error
	ip2regionOnce.Do(func() {
		ip2regionInstance = &IP2RegionFiller{}
		if err := ip2regionInstance.initialize(); err != nil {
			initErr = fmt.Errorf("failed to initialize IP2Region filler: %v", err)
			ip2regionInstance = nil
		}
	})

	if initErr != nil {
		return nil, initErr
	}

	if ip2regionInstance == nil {
		return nil, fmt.Errorf("IP2Region filler initialization failed")
	}

	return ip2regionInstance, nil
}

// GetIP2RegionInstance 获取IP2Region填充器单例实例
// 如果实例未初始化，返回nil
func GetIP2RegionInstance() *IP2RegionFiller {
	return ip2regionInstance
}

// IsIP2RegionInitialized 检查IP2Region填充器是否已初始化
func IsIP2RegionInitialized() bool {
	return ip2regionInstance != nil && ip2regionInstance.enabled
}

// initialize 初始化IP2Region搜索器
func (f *IP2RegionFiller) initialize() error {
	dbPath := conf.GlobalConfig.ModelIndex.IP2RegionDbPath
	if dbPath == "" {
		zaplog.Logger.Warn("IP2Region database path is not configured, disabling IP2Region filler")
		f.enabled = false
		return nil
	}

	// 从文件加载数据库
	cBuff, err := xdb.LoadContentFromFile(dbPath)
	if err != nil {
		// 数据库文件不存在时，禁用填充器而不是返回错误
		zaplog.Logger.Warn("Failed to load IP2Region database, disabling IP2Region filler",
			zap.String("dbPath", dbPath),
			zap.Error(err))
		f.enabled = false
		return nil
	}

	// 创建搜索器
	searcher, err := xdb.NewWithBuffer(cBuff)
	if err != nil {
		// 搜索器创建失败时，禁用填充器而不是返回错误
		zaplog.Logger.Warn("Failed to create IP2Region searcher, disabling IP2Region filler",
			zap.Error(err))
		f.enabled = false
		return nil
	}

	// 加载城市编码映射
	if err := f.loadCityCodeMap(); err != nil {
		zaplog.Logger.Warn("Failed to load city code mapping, using default mapping",
			zap.Error(err))
		// 即使加载失败也不禁用填充器，使用默认的哈希方式
	}

	f.searcher = searcher
	f.enabled = true
	zaplog.Logger.Info("IP2Region filler singleton initialized successfully",
		zap.String("dbPath", dbPath),
		zap.Int("dbSize", len(cBuff)),
		zap.Int("cityCodeMapSize", len(f.cityCodeMap)),
		zap.String("mode", "singleton"))
	return nil
}

// Name 返回填充器名称
func (f *IP2RegionFiller) Name() string {
	return "IP2Region"
}

// IsEnabled 检查填充器是否启用
func (f *IP2RegionFiller) IsEnabled() bool {
	return f.enabled
}

// Fill 填充城市编码信息
func (f *IP2RegionFiller) Fill(req *rtb_types.RTBBidRequest) error {
	if !f.enabled {
		return nil
	}

	if req.Device == nil {
		return fmt.Errorf("device is nil")
	}

	ipv4 := req.Device.Ip
	if ipv4 == "" {
		// IP为空时设置默认城市编码为0
		req.Device.GeoCity = 0
		return nil
	}

	// 验证IP地址格式
	if net.ParseIP(ipv4) == nil {
		zaplog.Logger.Warn("Invalid IP address", zap.String("ip", ipv4))
		req.Device.GeoCity = 0
		return nil
	}

	// 使用IP2Region查询地理位置信息
	region, err := f.searcher.SearchByStr(ipv4)
	if err != nil {
		zaplog.Logger.Error("Failed to search IP region", zap.String("ip", ipv4), zap.Error(err))
		req.Device.GeoCity = 0
		return nil
	}

	// 解析城市编码
	cityCode := f.parseCityCode(region)
	req.Device.GeoCity = int32(cityCode)

	zaplog.Logger.Debug("IP region lookup",
		zap.String("ip", ipv4),
		zap.String("region", region),
		zap.Int("cityCode", cityCode))

	return nil
}

// parseCityCode 从IP2Region返回的字符串中解析城市编码
// IP2Region返回格式: "国家|区域|省份|城市|ISP"
func (f *IP2RegionFiller) parseCityCode(region string) int {
	if region == "" {
		return 0
	}

	// 分割地理位置信息
	parts := strings.Split(region, "|")
	if len(parts) < 4 {
		return 0
	}

	city := strings.TrimSpace(parts[3])
	if city == "" || city == "0" {
		return 0
	}

	// 根据城市名称获取编码
	return f.getCityCodeByName(city)
}

// loadCityCodeMap 从citycode.txt文件加载城市编码映射
func (f *IP2RegionFiller) loadCityCodeMap() error {
	// 初始化映射
	f.cityCodeMap = make(map[string]int)

	// 构建citycode.txt文件路径
	cityCodePath := filepath.Join("data", "ip2region", "citycode.txt")

	// 打开文件
	file, err := os.Open(cityCodePath)
	if err != nil {
		return fmt.Errorf("failed to open citycode.txt: %v", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineNum := 0
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// 跳过空行和标题行
		if line == "" || lineNum == 1 {
			continue
		}

		// 解析每行数据：id super_code city_code city_name level
		fields := strings.Fields(line)
		if len(fields) < 4 {
			continue
		}

		// 提取城市编码和城市名称
		cityCodeStr := fields[2]
		cityName := fields[3]

		// 转换城市编码为整数
		cityCode, err := strconv.Atoi(cityCodeStr)
		if err != nil {
			zaplog.Logger.Warn("Invalid city code format",
				zap.String("line", line),
				zap.Int("lineNum", lineNum),
				zap.Error(err))
			continue
		}

		// 添加到映射中
		f.cityCodeMap[cityName] = cityCode
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading citycode.txt: %v", err)
	}

	zaplog.Logger.Info("City code mapping loaded successfully",
		zap.String("file", cityCodePath),
		zap.Int("totalCities", len(f.cityCodeMap)))

	return nil
}

// getCityCodeByName 根据城市名称获取城市编码
func (f *IP2RegionFiller) getCityCodeByName(cityName string) int {
	// 首先尝试从加载的映射中查找
	if f.cityCodeMap != nil {
		if code, exists := f.cityCodeMap[cityName]; exists {
			return code
		}
	}
	return 0
}
