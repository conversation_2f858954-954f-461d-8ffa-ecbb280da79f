// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: tensorflow/core/framework/resource_handle.proto

package framework

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Protocol buffer representing a handle to a tensorflow resource. Handles are
// not valid across executions, but can be serialized back and forth from within
// a single run.
type ResourceHandleProto struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique name for the device containing the resource.
	Device string `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// Container in which this resource is placed.
	Container string `protobuf:"bytes,2,opt,name=container,proto3" json:"container,omitempty"`
	// Unique name of this resource.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Hash code for the type of the resource. Is only valid in the same device
	// and in the same execution.
	HashCode uint64 `protobuf:"varint,4,opt,name=hash_code,json=hashCode,proto3" json:"hash_code,omitempty"`
	// For debug-only, the name of the type pointed to by this handle, if
	// available.
	MaybeTypeName string `protobuf:"bytes,5,opt,name=maybe_type_name,json=maybeTypeName,proto3" json:"maybe_type_name,omitempty"`
	// Data types and shapes for the underlying resource.
	DtypesAndShapes []*ResourceHandleProto_DtypeAndShape `protobuf:"bytes,6,rep,name=dtypes_and_shapes,json=dtypesAndShapes,proto3" json:"dtypes_and_shapes,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ResourceHandleProto) Reset() {
	*x = ResourceHandleProto{}
	mi := &file_tensorflow_core_framework_resource_handle_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceHandleProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceHandleProto) ProtoMessage() {}

func (x *ResourceHandleProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_resource_handle_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceHandleProto.ProtoReflect.Descriptor instead.
func (*ResourceHandleProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_resource_handle_proto_rawDescGZIP(), []int{0}
}

func (x *ResourceHandleProto) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

func (x *ResourceHandleProto) GetContainer() string {
	if x != nil {
		return x.Container
	}
	return ""
}

func (x *ResourceHandleProto) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceHandleProto) GetHashCode() uint64 {
	if x != nil {
		return x.HashCode
	}
	return 0
}

func (x *ResourceHandleProto) GetMaybeTypeName() string {
	if x != nil {
		return x.MaybeTypeName
	}
	return ""
}

func (x *ResourceHandleProto) GetDtypesAndShapes() []*ResourceHandleProto_DtypeAndShape {
	if x != nil {
		return x.DtypesAndShapes
	}
	return nil
}

// Protocol buffer representing a pair of (data type, tensor shape).
type ResourceHandleProto_DtypeAndShape struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Data type of the tensor.
	Dtype DataType `protobuf:"varint,1,opt,name=dtype,proto3,enum=tensorflow.DataType" json:"dtype,omitempty"`
	// Shape of the tensor.
	Shape         *TensorShapeProto `protobuf:"bytes,2,opt,name=shape,proto3" json:"shape,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceHandleProto_DtypeAndShape) Reset() {
	*x = ResourceHandleProto_DtypeAndShape{}
	mi := &file_tensorflow_core_framework_resource_handle_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceHandleProto_DtypeAndShape) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceHandleProto_DtypeAndShape) ProtoMessage() {}

func (x *ResourceHandleProto_DtypeAndShape) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_resource_handle_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceHandleProto_DtypeAndShape.ProtoReflect.Descriptor instead.
func (*ResourceHandleProto_DtypeAndShape) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_resource_handle_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ResourceHandleProto_DtypeAndShape) GetDtype() DataType {
	if x != nil {
		return x.Dtype
	}
	return DataType_DT_INVALID
}

func (x *ResourceHandleProto_DtypeAndShape) GetShape() *TensorShapeProto {
	if x != nil {
		return x.Shape
	}
	return nil
}

var File_tensorflow_core_framework_resource_handle_proto protoreflect.FileDescriptor

const file_tensorflow_core_framework_resource_handle_proto_rawDesc = "" +
	"\n" +
	"/tensorflow/core/framework/resource_handle.proto\x12\n" +
	"tensorflow\x1a,tensorflow/core/framework/tensor_shape.proto\x1a%tensorflow/core/framework/types.proto\"\xf6\x02\n" +
	"\x13ResourceHandleProto\x12\x16\n" +
	"\x06device\x18\x01 \x01(\tR\x06device\x12\x1c\n" +
	"\tcontainer\x18\x02 \x01(\tR\tcontainer\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1b\n" +
	"\thash_code\x18\x04 \x01(\x04R\bhashCode\x12&\n" +
	"\x0fmaybe_type_name\x18\x05 \x01(\tR\rmaybeTypeName\x12Y\n" +
	"\x11dtypes_and_shapes\x18\x06 \x03(\v2-.tensorflow.ResourceHandleProto.DtypeAndShapeR\x0fdtypesAndShapes\x1ao\n" +
	"\rDtypeAndShape\x12*\n" +
	"\x05dtype\x18\x01 \x01(\x0e2\x14.tensorflow.DataTypeR\x05dtype\x122\n" +
	"\x05shape\x18\x02 \x01(\v2\x1c.tensorflow.TensorShapeProtoR\x05shapeJ\x04\b\a\x10\bBa\n" +
	"\x18org.tensorflow.frameworkB\x0eResourceHandleP\x01Z0rtb_model_server/proto/tensorflow/core/framework\xf8\x01\x01b\x06proto3"

var (
	file_tensorflow_core_framework_resource_handle_proto_rawDescOnce sync.Once
	file_tensorflow_core_framework_resource_handle_proto_rawDescData []byte
)

func file_tensorflow_core_framework_resource_handle_proto_rawDescGZIP() []byte {
	file_tensorflow_core_framework_resource_handle_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_framework_resource_handle_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_tensorflow_core_framework_resource_handle_proto_rawDesc), len(file_tensorflow_core_framework_resource_handle_proto_rawDesc)))
	})
	return file_tensorflow_core_framework_resource_handle_proto_rawDescData
}

var file_tensorflow_core_framework_resource_handle_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tensorflow_core_framework_resource_handle_proto_goTypes = []any{
	(*ResourceHandleProto)(nil),               // 0: tensorflow.ResourceHandleProto
	(*ResourceHandleProto_DtypeAndShape)(nil), // 1: tensorflow.ResourceHandleProto.DtypeAndShape
	(DataType)(0),            // 2: tensorflow.DataType
	(*TensorShapeProto)(nil), // 3: tensorflow.TensorShapeProto
}
var file_tensorflow_core_framework_resource_handle_proto_depIdxs = []int32{
	1, // 0: tensorflow.ResourceHandleProto.dtypes_and_shapes:type_name -> tensorflow.ResourceHandleProto.DtypeAndShape
	2, // 1: tensorflow.ResourceHandleProto.DtypeAndShape.dtype:type_name -> tensorflow.DataType
	3, // 2: tensorflow.ResourceHandleProto.DtypeAndShape.shape:type_name -> tensorflow.TensorShapeProto
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_tensorflow_core_framework_resource_handle_proto_init() }
func file_tensorflow_core_framework_resource_handle_proto_init() {
	if File_tensorflow_core_framework_resource_handle_proto != nil {
		return
	}
	file_tensorflow_core_framework_tensor_shape_proto_init()
	file_tensorflow_core_framework_types_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_tensorflow_core_framework_resource_handle_proto_rawDesc), len(file_tensorflow_core_framework_resource_handle_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_framework_resource_handle_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_framework_resource_handle_proto_depIdxs,
		MessageInfos:      file_tensorflow_core_framework_resource_handle_proto_msgTypes,
	}.Build()
	File_tensorflow_core_framework_resource_handle_proto = out.File
	file_tensorflow_core_framework_resource_handle_proto_goTypes = nil
	file_tensorflow_core_framework_resource_handle_proto_depIdxs = nil
}
