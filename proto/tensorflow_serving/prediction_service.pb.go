// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: tensorflow_serving/prediction_service.proto

package tensorflow_serving

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_tensorflow_serving_prediction_service_proto protoreflect.FileDescriptor

const file_tensorflow_serving_prediction_service_proto_rawDesc = "" +
	"\n" +
	"+tensorflow_serving/prediction_service.proto\x12\x12tensorflow.serving\x1a tensorflow_serving/predict.proto2g\n" +
	"\x11PredictionService\x12R\n" +
	"\aPredict\x12\".tensorflow.serving.PredictRequest\x1a#.tensorflow.serving.PredictResponseBAZ<rtb_model_server/proto/tensorflow_serving;tensorflow_serving\xf8\x01\x01b\x06proto3"

var file_tensorflow_serving_prediction_service_proto_goTypes = []any{
	(*PredictRequest)(nil),  // 0: tensorflow.serving.PredictRequest
	(*PredictResponse)(nil), // 1: tensorflow.serving.PredictResponse
}
var file_tensorflow_serving_prediction_service_proto_depIdxs = []int32{
	0, // 0: tensorflow.serving.PredictionService.Predict:input_type -> tensorflow.serving.PredictRequest
	1, // 1: tensorflow.serving.PredictionService.Predict:output_type -> tensorflow.serving.PredictResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_tensorflow_serving_prediction_service_proto_init() }
func file_tensorflow_serving_prediction_service_proto_init() {
	if File_tensorflow_serving_prediction_service_proto != nil {
		return
	}
	file_tensorflow_serving_predict_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_tensorflow_serving_prediction_service_proto_rawDesc), len(file_tensorflow_serving_prediction_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_tensorflow_serving_prediction_service_proto_goTypes,
		DependencyIndexes: file_tensorflow_serving_prediction_service_proto_depIdxs,
	}.Build()
	File_tensorflow_serving_prediction_service_proto = out.File
	file_tensorflow_serving_prediction_service_proto_goTypes = nil
	file_tensorflow_serving_prediction_service_proto_depIdxs = nil
}
