// Protocol buffer representing the shape of tensors.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: tensorflow/core/framework/tensor_shape.proto

package framework

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Dimensions of a tensor.
type TensorShapeProto struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Dimensions of the tensor, such as {"input", 30}, {"output", 40}
	// for a 30 x 40 2D tensor. If an entry has size -1, this
	// corresponds to a dimension of unknown size. The names are
	// optional.
	//
	// The order of entries in "dim" matters: It indicates the layout of the
	// values in the tensor in-memory representation.
	//
	// The first entry in "dim" is the outermost dimension used to layout the
	// values, the last entry is the innermost dimension. This matches the
	// in-memory layout of RowMajor Eigen tensors.
	//
	// If "dim.size()" > 0, "unknown_rank" must be false.
	Dim []*TensorShapeProto_Dim `protobuf:"bytes,2,rep,name=dim,proto3" json:"dim,omitempty"`
	// If true, the number of dimensions in the shape is unknown.
	//
	// If true, "dim.size()" must be 0.
	UnknownRank   bool `protobuf:"varint,3,opt,name=unknown_rank,json=unknownRank,proto3" json:"unknown_rank,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TensorShapeProto) Reset() {
	*x = TensorShapeProto{}
	mi := &file_tensorflow_core_framework_tensor_shape_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TensorShapeProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorShapeProto) ProtoMessage() {}

func (x *TensorShapeProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_tensor_shape_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorShapeProto.ProtoReflect.Descriptor instead.
func (*TensorShapeProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_tensor_shape_proto_rawDescGZIP(), []int{0}
}

func (x *TensorShapeProto) GetDim() []*TensorShapeProto_Dim {
	if x != nil {
		return x.Dim
	}
	return nil
}

func (x *TensorShapeProto) GetUnknownRank() bool {
	if x != nil {
		return x.UnknownRank
	}
	return false
}

// One dimension of the tensor.
type TensorShapeProto_Dim struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Size of the tensor in that dimension.
	// This value must be >= -1, but values of -1 are reserved for "unknown"
	// shapes (values of -1 mean "unknown" dimension). Certain wrappers
	// that work with TensorShapeProto may fail at runtime when deserializing
	// a TensorShapeProto containing a dim value of -1.
	Size int64 `protobuf:"varint,1,opt,name=size,proto3" json:"size,omitempty"`
	// Optional name of the tensor dimension.
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TensorShapeProto_Dim) Reset() {
	*x = TensorShapeProto_Dim{}
	mi := &file_tensorflow_core_framework_tensor_shape_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TensorShapeProto_Dim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorShapeProto_Dim) ProtoMessage() {}

func (x *TensorShapeProto_Dim) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_tensor_shape_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorShapeProto_Dim.ProtoReflect.Descriptor instead.
func (*TensorShapeProto_Dim) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_tensor_shape_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TensorShapeProto_Dim) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *TensorShapeProto_Dim) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_tensorflow_core_framework_tensor_shape_proto protoreflect.FileDescriptor

const file_tensorflow_core_framework_tensor_shape_proto_rawDesc = "" +
	"\n" +
	",tensorflow/core/framework/tensor_shape.proto\x12\n" +
	"tensorflow\"\x98\x01\n" +
	"\x10TensorShapeProto\x122\n" +
	"\x03dim\x18\x02 \x03(\v2 .tensorflow.TensorShapeProto.DimR\x03dim\x12!\n" +
	"\funknown_rank\x18\x03 \x01(\bR\vunknownRank\x1a-\n" +
	"\x03Dim\x12\x12\n" +
	"\x04size\x18\x01 \x01(\x03R\x04size\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04nameBd\n" +
	"\x18org.tensorflow.frameworkB\x11TensorShapeProtosP\x01Z0rtb_model_server/proto/tensorflow/core/framework\xf8\x01\x01b\x06proto3"

var (
	file_tensorflow_core_framework_tensor_shape_proto_rawDescOnce sync.Once
	file_tensorflow_core_framework_tensor_shape_proto_rawDescData []byte
)

func file_tensorflow_core_framework_tensor_shape_proto_rawDescGZIP() []byte {
	file_tensorflow_core_framework_tensor_shape_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_framework_tensor_shape_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_tensorflow_core_framework_tensor_shape_proto_rawDesc), len(file_tensorflow_core_framework_tensor_shape_proto_rawDesc)))
	})
	return file_tensorflow_core_framework_tensor_shape_proto_rawDescData
}

var file_tensorflow_core_framework_tensor_shape_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tensorflow_core_framework_tensor_shape_proto_goTypes = []any{
	(*TensorShapeProto)(nil),     // 0: tensorflow.TensorShapeProto
	(*TensorShapeProto_Dim)(nil), // 1: tensorflow.TensorShapeProto.Dim
}
var file_tensorflow_core_framework_tensor_shape_proto_depIdxs = []int32{
	1, // 0: tensorflow.TensorShapeProto.dim:type_name -> tensorflow.TensorShapeProto.Dim
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tensorflow_core_framework_tensor_shape_proto_init() }
func file_tensorflow_core_framework_tensor_shape_proto_init() {
	if File_tensorflow_core_framework_tensor_shape_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_tensorflow_core_framework_tensor_shape_proto_rawDesc), len(file_tensorflow_core_framework_tensor_shape_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_framework_tensor_shape_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_framework_tensor_shape_proto_depIdxs,
		MessageInfos:      file_tensorflow_core_framework_tensor_shape_proto_msgTypes,
	}.Build()
	File_tensorflow_core_framework_tensor_shape_proto = out.File
	file_tensorflow_core_framework_tensor_shape_proto_goTypes = nil
	file_tensorflow_core_framework_tensor_shape_proto_depIdxs = nil
}
