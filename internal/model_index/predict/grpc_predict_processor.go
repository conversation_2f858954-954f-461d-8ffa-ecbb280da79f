package predict

import (
	"context"
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/common/domob_thrift/rtb_types"
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/zaplog"
	"time"

	"go.uber.org/zap"
)

// GRPCPredictProcessor 用于调用TFServing
type GRPCPredictProcessor struct {
	featureProcessor *FeatureProcessor
	tfservingClient  *TFServingClient
}

// NewGRPCPredictProcessor 创建新的gRPC预测处理器
func NewGRPCPredictProcessor(featureConfigPath string, tfservingAddr string, modelName string) (*GRPCPredictProcessor, error) {
	// 初始化特征处理器
	featureProcessor, err := NewFeatureProcessor(featureConfigPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create feature processor: %w", err)
	}

	// 初始化TFServing客户端
	tfservingClient, err := NewTFServingClient(tfservingAddr, modelName)
	if err != nil {
		return nil, fmt.Errorf("failed to create tfserving client: %w", err)
	}

	return &GRPCPredictProcessor{
		featureProcessor: featureProcessor,
		tfservingClient:  tfservingClient,
	}, nil
}

// Close 关闭gRPC预测处理器
func (p *GRPCPredictProcessor) Close() {
	if p.tfservingClient != nil {
		p.tfservingClient.Close()
	}
}

// GRPCPredictResult gRPC预测结果
type GRPCPredictResult struct {
	CreativeId  int32
	BillingRate float32
	ImpRate     float32
	Theta1      float32
	Theta2      float32
}

// Predict 使用gRPC调用TFServing进行预测
func (p *GRPCPredictProcessor) Predict(requestCtx *ctx.RequestContext, creativeIds []int32) (map[int32]GRPCPredictResult, error) {
	// 提取特征
	batchFeatures, err := p.extractFeaturesFromRTBRequest(requestCtx, creativeIds)
	if err != nil {
		return nil, fmt.Errorf("failed to extract features: %w", err)
	}

	// 调用TFServing
	ctx := context.Background()
	grpcResponse, err := p.tfservingClient.Predict(ctx, batchFeatures)
	if err != nil {
		return nil, fmt.Errorf("failed to call tfserving: %w", err)
	}

	// 转换结果
	results := p.convertToGRPCResults(grpcResponse, creativeIds)
	return results, nil
}

// extractFeaturesFromRTBRequest 从RTB请求中提取特征
func (p *GRPCPredictProcessor) extractFeaturesFromRTBRequest(requestCtx *ctx.RequestContext, creativeIds []int32) ([]map[string]interface{}, error) {
	batchFeatures := make([]map[string]interface{}, len(creativeIds))
	bidReq := requestCtx.BidRequest

	for i, creativeId := range creativeIds {
		rawFeatures := make(map[string]interface{})

		// 从ADX的RTB请求中解析特征
		if err := p.extractAllFeaturesFromRTB(bidReq, creativeId, rawFeatures); err != nil {
			return nil, fmt.Errorf("failed to extract features for creative %d: %w", creativeId, err)
		}

		// 从数据库获取创意、策略等信息 - 参考Thrift的buildAdList
		if err := p.extractDatabaseFeatures(requestCtx, creativeId, rawFeatures); err != nil {
			zaplog.Logger.Error("Failed to extract database features",
				zap.Int32("creative_id", creativeId),
				zap.Error(err))
			// 继续处理其他创意，不中断整个批次
			continue
		}

		// 使用特征处理器处理特征
		processedFeatures, err := p.featureProcessor.ProcessFeatures(rawFeatures)
		if err != nil {
			zaplog.Logger.Error("Failed to process features",
				zap.Int32("creative_id", creativeId),
				zap.Error(err))
			return nil, err
		}

		batchFeatures[i] = processedFeatures
	}

	return batchFeatures, nil
}

// extractAllFeaturesFromRTB 参考Thrift流程从ADX的RTB请求中解析所有特征
func (p *GRPCPredictProcessor) extractAllFeaturesFromRTB(bidReq *rtb_types.RTBBidRequest, creativeId int32, features map[string]interface{}) error {
	// 基础特征 - 参考Thrift的buildRequestHeader
	features["dsp_id"] = int64(1) // 默认DSP ID
	features["exchange_id"] = int64(bidReq.ExchangeId)
	features["creative_id"] = int64(creativeId)

	// 时间特征
	now := time.Now()
	features["hour"] = int64(now.Hour())
	features["week_day"] = int64(now.Weekday())

	// 设备相关特征 - 参考Thrift流程
	if bidReq.Device != nil {
		features["dm_platform"] = int64(bidReq.Device.DmPlatform)

		// TODO: 设备ID处理需要验证字段名称是否正确
		// 设备ID处理 - 参考Thrift中的deviceIdMd5逻辑
		var deviceIdHash int64
		if bidReq.Device.DmPlatform == 1 { // Android
			deviceIdHash = p.convertStringToInt64(bidReq.Device.Oaidmd5)
		} else { // iOS
			deviceIdHash = p.convertStringToInt64(bidReq.Device.Idfamd5)
		}
		features["device_id"] = deviceIdHash

		// 地理位置特征
		features["dm_geo_id"] = int64(bidReq.Device.DmGeoId)
		features["dm_os_id"] = int64(bidReq.Device.DmOsId)
		features["province"] = int64(bidReq.Device.GeoRegion)
		features["city"] = int64(bidReq.Device.GeoCity)
		features["country"] = int64(bidReq.Device.GeoCountry)
		features["client_ip"] = p.convertIPToInt64(bidReq.Device.Ip)

		// 设备信息
		features["dev_make"] = p.convertStringToInt64(bidReq.Device.DevMake)
		features["dev_model"] = p.convertStringToInt64(bidReq.Device.DevModel)
		features["standard_make"] = p.convertStringToInt64(bidReq.Device.DevMake)
		features["dm_accesstype_id"] = int64(bidReq.Device.DmAccesstypeId)
	}

	// 应用特征 - 参考Thrift流程
	if bidReq.App != nil {
		features["dm_media_id"] = int64(bidReq.App.DmMediaId)
		features["app_bundle"] = p.convertStringToInt64(bidReq.App.AppBundle)
	}

	// 请求特征 - 参考Thrift流程
	if len(bidReq.BidList) > 0 {
		firstBid := bidReq.BidList[0]
		if firstBid.Request != nil {
			features["inventory_type"] = int64(firstBid.Request.InventoryType)
			features["adp_id"] = p.convertStringToInt64(firstBid.Request.AdpId)
			features["bidfloor"] = int64(firstBid.Request.Bidfloor)
			features["adp_dim"] = int64(firstBid.Request.AdpDim)
			features["media_bid_type"] = int64(firstBid.Request.SupportMediaBidType)

			// 绝对位置 - 直接使用int32值
			features["absolute_pos"] = int64(firstBid.Request.AbsolutePos)
		}
	}

	// ADX交换机ID
	features["adx_exchange_id"] = int64(bidReq.AdxExchangeId)

	// 设置YAML配置中定义但RTB请求中可能缺失的特征默认值
	p.setMissingFeatures(features)

	return nil
}

// extractDatabaseFeatures 从数据库获取创意、策略等信息 - 参考Thrift的buildAdList
func (p *GRPCPredictProcessor) extractDatabaseFeatures(requestCtx *ctx.RequestContext, creativeId int32, features map[string]interface{}) error {
	// 获取创意信息
	creative, err := requestCtx.AdIndex.GetCreative(creativeId)
	if err != nil {
		return fmt.Errorf("failed to get creative: %w", err)
	}

	// 获取策略信息
	strategy, err := requestCtx.AdIndex.GetStrategy(creative.StrategyId)
	if err != nil {
		return fmt.Errorf("failed to get strategy: %w", err)
	}

	// 获取跟踪信息
	var tracking *rtb_adinfo_types.AdTrackingInfo
	if len(creative.AdTrackingIds) > 0 {
		tracking, err = requestCtx.AdIndex.GetAdTracking(creative.AdTrackingIds[0])
		if err != nil {
			return fmt.Errorf("failed to get tracking: %w", err)
		}
	}

	// 设置数据库相关特征
	features["strategy_id"] = int64(creative.StrategyId)
	features["campaign_id"] = int64(creative.CampaignId)
	features["sponsor_id"] = int64(creative.SponsorId)
	features["creative_type"] = int64(creative.DisplayType)
	features["dsp_cost_mod"] = int64(strategy.MediaCostType)
	features["media_bid_type"] = int64(strategy.MediaBidType)
	features["dsp_bid"] = int64(strategy.Price) // DSP出价

	// 跟踪相关特征
	if tracking != nil {
		features["product_id"] = int64(tracking.ProductId)
		features["chn_id"] = int64(tracking.ChnId)
		features["app_id"] = int64(tracking.AppId)
	}

	// AdMatchType - 参考Thrift中的逻辑
	var adMatchType int64
	if len(creative.Containers) > 0 {
		adMatchType = creative.Containers[0].AdMatchType
	}
	features["ad_pos_id"] = adMatchType

	return nil
}

// extractRequestFeatures 从ADX请求中提取特征值
func (p *GRPCPredictProcessor) extractRequestFeatures(bidReq *rtb_types.RTBBidRequest, features map[string]interface{}) error {
	if len(bidReq.BidList) == 0 {
		return fmt.Errorf("no bid list in request")
	}

	firstBid := bidReq.BidList[0]
	if firstBid.Request == nil {
		return fmt.Errorf("no request info in bid")
	}

	features["inventory_type"] = int64(firstBid.Request.InventoryType)
	features["adp_id"] = p.convertStringToInt64(firstBid.Request.AdpId)
	features["bidfloor"] = int64(firstBid.Request.Bidfloor)
	features["adp_dim"] = int64(firstBid.Request.AdpDim)
	features["media_bid_type"] = int64(firstBid.Request.SupportMediaBidType)

	if bidReq.Device == nil {
		return fmt.Errorf("no device info in request")
	}

	features["dm_platform"] = int64(bidReq.Device.DmPlatform)
	features["dev_make"] = p.convertStringToInt64(bidReq.Device.DevMake)
	features["dev_model"] = p.convertStringToInt64(bidReq.Device.DevModel)
	features["standard_make"] = p.convertStringToInt64(bidReq.Device.DevMake)
	features["client_ip"] = p.convertIPToInt64(bidReq.Device.Ip)
	features["province"] = int64(bidReq.Device.GeoRegion)
	features["city"] = int64(bidReq.Device.GeoCity)
	features["country"] = int64(bidReq.Device.GeoCountry)

	if bidReq.App == nil {
		return fmt.Errorf("no app info in request")
	}

	features["app_bundle"] = p.convertStringToInt64(bidReq.App.AppBundle)
	features["dm_media_id"] = int64(bidReq.App.DmMediaId)

	return nil
}

// setMissingFeatures 基于FeatureProcessor配置自动设置缺失特征的默认值
func (p *GRPCPredictProcessor) setMissingFeatures(features map[string]interface{}) {
	// 从FeatureProcessor获取所有配置的特征名称
	if p.featureProcessor == nil {
		return
	}

	// 遍历YAML配置中的所有特征，为缺失的特征设置默认值
	for featureName := range p.featureProcessor.config.Features {
		if _, exists := features[featureName]; !exists {
			// TODO: 优化特征提取，减少默认值依赖
			// 这些特征应该从来源获取真实值：

			// 使用YAML配置中定义的默认值
			if typeConfig, ok := p.featureProcessor.config.Types[p.featureProcessor.config.Features[featureName].Type]; ok {
				features[featureName] = typeConfig.Default
			} else {
				features[featureName] = int64(0) // 兜底默认值
			}
		}
	}

	// 确保dsp_bid存在
	if _, exists := features["dsp_bid"]; !exists {
		// TODO: dsp_bid应该从策略配置或实时出价算法获取，而不是使用固定默认值
		features["dsp_bid"] = int64(100000) // 默认出价（分）
	}
}

// convertStringToInt64 将字符串转换为int64（使用signature.go的hash算法）
func (p *GRPCPredictProcessor) convertStringToInt64(str string) int64 {
	if str == "" {
		return 0
	}
	// 使用signature.go中的hash算法，与模型训练时保持一致
	hashValue := Get48SignFromInput(str)
	return int64(hashValue)
}

// convertIPToInt64 将IP地址转换为int64（使用signature.go的hash算法）
func (p *GRPCPredictProcessor) convertIPToInt64(ip string) int64 {
	if ip == "" {
		return 0
	}
	// 使用signature.go中的hash算法处理IP地址
	hashValue := Get48SignFromInput(ip)
	return int64(hashValue)
}

// convertToGRPCResults 将gRPC响应转换为结果格式
func (p *GRPCPredictProcessor) convertToGRPCResults(grpcResponse map[string]interface{}, creativeIds []int32) map[int32]GRPCPredictResult {
	results := make(map[int32]GRPCPredictResult)

	// 解析TFServing的返回结果
	// 根据TFServing模型的输出格式解析
	for i, creativeId := range creativeIds {
		result := GRPCPredictResult{
			CreativeId: creativeId,
		}

		// 从grpcResponse中提取预测结果
		// 这里需要根据实际的TFServing返回格式来解析
		if billingRates, ok := grpcResponse["billing_rate"].([]float32); ok && i < len(billingRates) {
			result.BillingRate = billingRates[i]
		}
		if impRates, ok := grpcResponse["imp_rate"].([]float32); ok && i < len(impRates) {
			result.ImpRate = impRates[i]
		}
		if theta1s, ok := grpcResponse["theta1"].([]float32); ok && i < len(theta1s) {
			result.Theta1 = theta1s[i]
		}
		if theta2s, ok := grpcResponse["theta2"].([]float32); ok && i < len(theta2s) {
			result.Theta2 = theta2s[i]
		}

		results[creativeId] = result
	}

	return results
}
