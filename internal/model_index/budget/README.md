# 统计文件轮询监控功能

## 概述

本模块实现了基于轮询的统计文件监控功能，用于监控指定目录下的统计文件变化并自动加载最新数据。相比事件通知方案，轮询方案具有更好的兼容性和稳定性。

## 功能特性

### 1. 自动加载最新文件
- 程序启动时自动加载最新的统计文件
- 根据文件名中的时间戳判断最新文件
- 文件命名格式：`stats.data.yyyymmddhhiiss`

### 2. 轮询监控
- 可配置轮询间隔，默认30秒
- 定期检查目录中是否有新文件或文件修改
- 发现新文件或文件变化后自动加载
- 相比文件系统事件通知，轮询方案具有更好的跨平台兼容性

### 3. 文件格式支持
- 支持时间戳格式：yyyymmddhhiiss（年月日时分秒）
- 文件名模式：`{StatsFileNamePattern}.{timestamp}`
- 例如：`stats.data.20240115143000`

## 配置说明

在 `rtb_model_server.yaml` 中配置：

```yaml
BudgetConfig:
  # 统计文件目录
  StatsFileDir: data/budget/
  # 统计文件名模板（不包含时间戳）
  StatsFileNamePattern: stats.data
  # 轮询间隔（秒）
  PollingInterval: 30
```

## 工作流程

1. **启动时加载**：程序启动时扫描配置目录，找到最新的统计文件并加载
2. **轮询检查**：按配置的间隔定期检查目录
3. **新文件检测**：发现新文件或文件修改时自动重新加载
4. **数据更新**：保持统计数据与最新文件同步

## 使用方法

```go
// 获取统计处理器（自动启动轮询）
statsProcessor := budget.GetStatsProcessor()

// 手动停止轮询
statsProcessor.StopPolling()

// 手动加载统计数据
err := statsProcessor.LoadStats()
if err != nil {
    log.Printf("加载统计数据失败: %v", err)
}
```

## 日志说明

系统会记录以下关键日志：

- 轮询启动：`polling initialized`
- 发现新文件：`found new stats file, loading`
- 文件修改：`stats file modified, reloading`
- 加载成功：`stats reloaded successfully (polling)`
- 轮询停止：`polling stopped`