syntax = "proto3";

package tensorflow.serving;

option cc_enable_arenas = true;
option go_package = "rtb_model_server/proto/tensorflow_serving;tensorflow_serving";

import "tensorflow_serving/predict.proto";

// PredictionService provides access to machine-learned models loaded by
// model_servers.
service PredictionService {
  // Predict -- provides access to loaded TensorFlow model.
  rpc Predict(PredictRequest) returns (PredictResponse);
}
