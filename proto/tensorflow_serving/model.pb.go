// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: tensorflow_serving/model.proto

package tensorflow_serving

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Metadata for an inference request such as the model name and version.
type ModelSpec struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required servable name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Optional choice of which version of the model to use.
	//
	// Expected to be left unset in the common case. Should be specified when
	// there is a strong version consistency requirement (e.g. when the model
	// signature changes across versions and requests need to be
	// version-specific).
	//
	// When left unspecified, the system will serve the best available version.
	// This is typically the latest version, though during version transitions,
	// notably when serving on a fleet of instances, may be either the previous or
	// new version.
	//
	// Types that are valid to be assigned to VersionChoice:
	//
	//	*ModelSpec_Version
	//	*ModelSpec_VersionLabel
	VersionChoice isModelSpec_VersionChoice `protobuf_oneof:"version_choice"`
	// A named signature to evaluate. If unspecified, the default signature will
	// be used.
	SignatureName string `protobuf:"bytes,3,opt,name=signature_name,json=signatureName,proto3" json:"signature_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModelSpec) Reset() {
	*x = ModelSpec{}
	mi := &file_tensorflow_serving_model_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSpec) ProtoMessage() {}

func (x *ModelSpec) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_serving_model_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSpec.ProtoReflect.Descriptor instead.
func (*ModelSpec) Descriptor() ([]byte, []int) {
	return file_tensorflow_serving_model_proto_rawDescGZIP(), []int{0}
}

func (x *ModelSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelSpec) GetVersionChoice() isModelSpec_VersionChoice {
	if x != nil {
		return x.VersionChoice
	}
	return nil
}

func (x *ModelSpec) GetVersion() *wrapperspb.Int64Value {
	if x != nil {
		if x, ok := x.VersionChoice.(*ModelSpec_Version); ok {
			return x.Version
		}
	}
	return nil
}

func (x *ModelSpec) GetVersionLabel() string {
	if x != nil {
		if x, ok := x.VersionChoice.(*ModelSpec_VersionLabel); ok {
			return x.VersionLabel
		}
	}
	return ""
}

func (x *ModelSpec) GetSignatureName() string {
	if x != nil {
		return x.SignatureName
	}
	return ""
}

type isModelSpec_VersionChoice interface {
	isModelSpec_VersionChoice()
}

type ModelSpec_Version struct {
	// Use this specific version number.
	Version *wrapperspb.Int64Value `protobuf:"bytes,2,opt,name=version,proto3,oneof"`
}

type ModelSpec_VersionLabel struct {
	// Use the version associated with the given label.
	VersionLabel string `protobuf:"bytes,4,opt,name=version_label,json=versionLabel,proto3,oneof"`
}

func (*ModelSpec_Version) isModelSpec_VersionChoice() {}

func (*ModelSpec_VersionLabel) isModelSpec_VersionChoice() {}

var File_tensorflow_serving_model_proto protoreflect.FileDescriptor

const file_tensorflow_serving_model_proto_rawDesc = "" +
	"\n" +
	"\x1etensorflow_serving/model.proto\x12\x12tensorflow.serving\x1a\x1egoogle/protobuf/wrappers.proto\"\xb8\x01\n" +
	"\tModelSpec\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x127\n" +
	"\aversion\x18\x02 \x01(\v2\x1b.google.protobuf.Int64ValueH\x00R\aversion\x12%\n" +
	"\rversion_label\x18\x04 \x01(\tH\x00R\fversionLabel\x12%\n" +
	"\x0esignature_name\x18\x03 \x01(\tR\rsignatureNameB\x10\n" +
	"\x0eversion_choiceBAZ<rtb_model_server/proto/tensorflow_serving;tensorflow_serving\xf8\x01\x01b\x06proto3"

var (
	file_tensorflow_serving_model_proto_rawDescOnce sync.Once
	file_tensorflow_serving_model_proto_rawDescData []byte
)

func file_tensorflow_serving_model_proto_rawDescGZIP() []byte {
	file_tensorflow_serving_model_proto_rawDescOnce.Do(func() {
		file_tensorflow_serving_model_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_tensorflow_serving_model_proto_rawDesc), len(file_tensorflow_serving_model_proto_rawDesc)))
	})
	return file_tensorflow_serving_model_proto_rawDescData
}

var file_tensorflow_serving_model_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_tensorflow_serving_model_proto_goTypes = []any{
	(*ModelSpec)(nil),             // 0: tensorflow.serving.ModelSpec
	(*wrapperspb.Int64Value)(nil), // 1: google.protobuf.Int64Value
}
var file_tensorflow_serving_model_proto_depIdxs = []int32{
	1, // 0: tensorflow.serving.ModelSpec.version:type_name -> google.protobuf.Int64Value
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tensorflow_serving_model_proto_init() }
func file_tensorflow_serving_model_proto_init() {
	if File_tensorflow_serving_model_proto != nil {
		return
	}
	file_tensorflow_serving_model_proto_msgTypes[0].OneofWrappers = []any{
		(*ModelSpec_Version)(nil),
		(*ModelSpec_VersionLabel)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_tensorflow_serving_model_proto_rawDesc), len(file_tensorflow_serving_model_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_serving_model_proto_goTypes,
		DependencyIndexes: file_tensorflow_serving_model_proto_depIdxs,
		MessageInfos:      file_tensorflow_serving_model_proto_msgTypes,
	}.Build()
	File_tensorflow_serving_model_proto = out.File
	file_tensorflow_serving_model_proto_goTypes = nil
	file_tensorflow_serving_model_proto_depIdxs = nil
}
