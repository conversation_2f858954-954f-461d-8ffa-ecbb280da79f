package predict

import (
	"log"
	"rtb_model_server/common/domob_thrift/predict_model_server"
	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"
	"sync"
	"time"

	"git.apache.org/thrift.git/lib/go/thrift"
	"go.uber.org/zap"
)

// ThriftConnection 表示一个Thrift连接
type ThriftConnection struct {
	id                int64 // 连接唯一ID
	transport         *thrift.TSocket
	bufferedTransport thrift.TTransport // 保存 buffered transport 引用
	client            *predict_model_server.PredictModelServerClient
	lastUsed          time.Time
}

// ConnectionPool 连接池管理器
type ConnectionPool struct {
	connChan     chan *ThriftConnection // 空闲连接队列
	poolSize     int
	maxIdleConns int
	connTimeout  time.Duration
	addr         string
	mu           sync.Mutex // 仅用于保护 nextConnID
	nextConnID   int64      // 下一个连接ID计数器
}

// NewConnectionPool 创建新的连接池
func NewConnectionPool(addr string, poolSize, maxIdleConns int, connTimeout time.Duration) *ConnectionPool {
	log.Println("new predict server thrift connection pool, addr: ", addr, "protocol: ", conf.GlobalConfig.PredictServer.ProtocolType)
	p := &ConnectionPool{
		poolSize:     poolSize,
		maxIdleConns: maxIdleConns,
		connTimeout:  connTimeout,
		addr:         addr,
		connChan:     make(chan *ThriftConnection, maxIdleConns),
	}

	// 初始化连接池
	var errorCount int
	var lastError error
	for i := 0; i < maxIdleConns; i++ {
		conn, err := p.createConnection()
		if err != nil {
			lastError = err
			errorCount++
			zaplog.Logger.Error("failed to create initial connection", zap.Int("index", i), zap.Error(err))
			continue
		}
		// 将连接放入 channel
		p.connChan <- conn
	}
	if errorCount > 0 {
		log.Println("[Warn] init predict server thrift connection error count: ", errorCount, "; last error: ", lastError)
	}

	return p
}

// createConnection 创建新的连接
func (p *ConnectionPool) createConnection() (*ThriftConnection, error) {
	transport, err := thrift.NewTSocket(p.addr)
	if err != nil {
		return nil, err
	}

	// 设置连接超时
	transport.SetTimeout(p.connTimeout)

	// 打开连接
	if err := transport.Open(); err != nil {
		return nil, err
	}

	// 生成连接ID
	p.nextConnID++
	connID := p.nextConnID

	// 根据配置选择协议类型
	var protocolFactory thrift.TProtocolFactory
	var actualTransport thrift.TTransport

	zaplog.Logger.Debug("create predict server thrift connection, protocol ", zap.String("addr", p.addr), zap.String("protocol", conf.GlobalConfig.PredictServer.ProtocolType), zap.Int64("connID", connID))

	if conf.GlobalConfig.PredictServer.ProtocolType == "buffered" {
		// 使用buffered协议
		bufferedTransport := thrift.NewTBufferedTransport(transport, 8192)
		actualTransport = bufferedTransport
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		client := predict_model_server.NewPredictModelServerClientFactory(bufferedTransport, protocolFactory)
		return &ThriftConnection{
			id:                connID,
			transport:         transport,
			bufferedTransport: bufferedTransport,
			client:            client,
			lastUsed:          time.Now(),
		}, nil
	} else {
		// 使用binary协议（默认）
		actualTransport = transport
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
	}
	client := predict_model_server.NewPredictModelServerClientFactory(actualTransport, protocolFactory)

	return &ThriftConnection{
		id:                connID,
		transport:         transport,
		bufferedTransport: nil,
		client:            client,
		lastUsed:          time.Now(),
	}, nil
}

// GetConnection 从连接池获取连接
func (p *ConnectionPool) GetConnection() (*ThriftConnection, error) {
	// 尝试从 channel 获取空闲连接
	select {
	case conn := <-p.connChan:
		// 检查连接是否还有效
		if p.isConnectionValid(conn) {
			conn.lastUsed = time.Now()
			zaplog.Logger.Debug("reusing connection from pool", zap.Int64("connID", conn.id))
			return conn, nil
		} else {
			// 连接已断开，关闭旧连接并重新创建
			zaplog.Logger.Debug("connection invalid, recreating", zap.Int64("oldConnID", conn.id))
			p.closeConnection(conn)
			newConn, err := p.createConnection()
			if err != nil {
				return nil, err
			}
			newConn.lastUsed = time.Now()
			return newConn, nil
		}
	default:
		// 无空闲连接，创建新连接
		conn, err := p.createConnection()
		zaplog.Logger.Info("get connection from pool, create new connection")
		if err != nil {
			zaplog.Logger.Error("get connection from pool, create new connection error")
			return nil, err
		}
		conn.lastUsed = time.Now()
		zaplog.Logger.Debug("created new connection for pool", zap.Int64("connID", conn.id))
		return conn, nil
	}
}

// ReleaseConnection 释放连接回连接池
func (p *ConnectionPool) ReleaseConnection(conn *ThriftConnection) {
	if conn == nil {
		return
	}

	// 对于 buffered transport，需要刷新缓冲区确保数据发送完成
	if conn.bufferedTransport != nil {
		if err := conn.bufferedTransport.Flush(); err != nil {
			zaplog.Logger.Error("failed to flush buffered transport", zap.Int64("connID", conn.id), zap.Error(err))
			p.closeConnection(conn)
			return
		}
	}

	// 检查连接是否仍然有效
	if !p.isConnectionValid(conn) {
		zaplog.Logger.Debug("connection invalid during release, closing", zap.Int64("connID", conn.id))
		p.closeConnection(conn)
		return
	}

	conn.lastUsed = time.Now()

	// 使用非阻塞发送将连接放回 channel，如果 channel 满了就关闭连接
	select {
	case p.connChan <- conn:
		zaplog.Logger.Debug("released connection back to pool", zap.Int64("connID", conn.id))
	default:
		// channel 满了，关闭连接
		zaplog.Logger.Debug("connection pool full, closing connection", zap.Int64("connID", conn.id))
		p.closeConnection(conn)
	}
}

// Close 关闭连接池中的所有连接
func (p *ConnectionPool) Close() {
	// 关闭 channel 中的所有连接
	for {
		select {
		case conn := <-p.connChan:
			p.closeConnection(conn)
		default:
			return
		}
	}
}

// isConnectionValid 检查连接是否有效
func (p *ConnectionPool) isConnectionValid(conn *ThriftConnection) bool {
	if conn == nil {
		return false
	}

	// 检查底层 socket 是否打开
	if conn.transport == nil || !conn.transport.IsOpen() {
		return false
	}

	// 如果使用了 buffered transport，也要检查它的状态
	if conn.bufferedTransport != nil {
		return conn.bufferedTransport.IsOpen()
	}

	return true
}

// closeConnection 正确关闭单个连接
func (p *ConnectionPool) closeConnection(conn *ThriftConnection) {
	if conn == nil {
		return
	}
	zaplog.Logger.Debug("close predict server thrift connection", zap.String("addr", p.addr), zap.Int64("connID", conn.id))

	// 如果使用了 buffered transport，先关闭它（会自动刷新缓冲区）
	if conn.bufferedTransport != nil {
		if conn.bufferedTransport.IsOpen() {
			conn.bufferedTransport.Close()
		}
	}

	// 然后关闭底层 socket
	if conn.transport != nil && conn.transport.IsOpen() {
		conn.transport.Close()
	}
}

// GetID 获取连接ID
func (conn *ThriftConnection) GetID() int64 {
	return conn.id
}

// GetPoolStats 获取连接池统计信息
func (p *ConnectionPool) GetPoolStats() (total, inUse, idle int) {
	idle = len(p.connChan)
	return p.poolSize, p.poolSize - idle, idle
}
