syntax = "proto3";

package tensorflow;

option cc_enable_arenas = true;
option java_outer_classname = "TypesProtos";
option java_multiple_files = true;
option java_package = "org.tensorflow.framework";
option go_package = "rtb_model_server/proto/tensorflow/core/framework";

// (== suppress_warning documentation-presence ==)
// LINT.IfChange
enum DataType {
  // Not a legal value for DataType. Used to indicate a DataType field
  // has not been set.
  DT_INVALID = 0;

  // Data types that all computation devices are expected to be
  // capable to support.
  DT_FLOAT = 1;
  DT_DOUBLE = 2;
  DT_INT32 = 3;
  DT_UINT8 = 4;
  DT_INT16 = 5;
  DT_INT8 = 6;
  DT_STRING = 7;
  DT_COMPLEX64 = 8;  // Single-precision complex
  DT_INT64 = 9;
  DT_BOOL = 10;
  DT_QINT8 = 11;     // Quantized int8
  DT_QUINT8 = 12;    // Quantized uint8
  DT_QINT32 = 13;    // Quantized int32
  DT_BFLOAT16 = 14;  // Float32 truncated to 16 bits.
  DT_QINT16 = 15;    // Quantized int16
  DT_QUINT16 = 16;   // Quantized uint16
  DT_UINT16 = 17;
  DT_COMPLEX128 = 18;  // Double-precision complex
  DT_HALF = 19;
  DT_RESOURCE = 20;
  DT_VARIANT = 21;  // Arbitrary C++ data types
  DT_UINT32 = 22;
  DT_UINT64 = 23;
  DT_FLOAT8_E5M2 = 24;      // 5 exponent bits, 2 mantissa bits.
  DT_FLOAT8_E4M3FN = 25;    // 4 exponent bits, 3 mantissa bits, finite-only, with
                            // 2 NaNs (0bS1111111).
  DT_FLOAT8_E4M3FNUZ = 26;  // 4 exponent bits, 3 mantissa bits, finite-only,
                            // with NaN.
  DT_FLOAT8_E4M3B11FNUZ = 27;  // 4 exponent bits, 3 mantissa bits, 11 bits
                               // bias, finite-only, with NaNs.
  DT_FLOAT8_E5M2FNUZ = 28;     // 5 exponent bits, 2 mantissa bits, finite-only,
                               // with NaN.
  DT_INT4 = 29;
  DT_UINT4 = 30;
  DT_INT2 = 31;
  DT_UINT2 = 32;

  // Do not use! These are only for TF1's obsolete reference Variables.
  // Every enum above should have a corresponding value below (verified by
  // types_test).
  DT_FLOAT_REF = 101;
  DT_DOUBLE_REF = 102;
  DT_INT32_REF = 103;
  DT_UINT8_REF = 104;
  DT_INT16_REF = 105;
  DT_INT8_REF = 106;
  DT_STRING_REF = 107;
  DT_COMPLEX64_REF = 108;
  DT_INT64_REF = 109;
  DT_BOOL_REF = 110;
  DT_QINT8_REF = 111;
  DT_QUINT8_REF = 112;
  DT_QINT32_REF = 113;
  DT_BFLOAT16_REF = 114;
  DT_QINT16_REF = 115;
  DT_QUINT16_REF = 116;
  DT_UINT16_REF = 117;
  DT_COMPLEX128_REF = 118;
  DT_HALF_REF = 119;
  DT_RESOURCE_REF = 120;
  DT_VARIANT_REF = 121;
  DT_UINT32_REF = 122;
  DT_UINT64_REF = 123;
  DT_FLOAT8_E5M2_REF = 124;
  DT_FLOAT8_E4M3FN_REF = 125;
  DT_FLOAT8_E4M3FNUZ_REF = 126;
  DT_FLOAT8_E4M3B11FNUZ_REF = 127;
  DT_FLOAT8_E5M2FNUZ_REF = 128;
  DT_INT4_REF = 129;
  DT_UINT4_REF = 130;
  DT_INT2_REF = 131;
  DT_UINT2_REF = 132;
}

// Represents a serialized tf.dtypes.Dtype
message SerializedDType {
  DataType datatype = 1;
}
