// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: tensorflow/core/framework/tensor.proto

package framework

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Protocol buffer representing a tensor.
type TensorProto struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Data type of the tensor.
	Dtype DataType `protobuf:"varint,1,opt,name=dtype,proto3,enum=tensorflow.DataType" json:"dtype,omitempty"`
	// Shape of the tensor. TODO(touts): sort out the 0-rank issues.
	TensorShape *TensorShapeProto `protobuf:"bytes,2,opt,name=tensor_shape,json=tensorShape,proto3" json:"tensor_shape,omitempty"`
	// Version number.
	//
	// In version 0, if the "repeated xxx" representations contain only one
	// element, that element is repeated to fill the shape. This makes it easy
	// to represent a constant Tensor with a single value.
	VersionNumber int32 `protobuf:"varint,3,opt,name=version_number,json=versionNumber,proto3" json:"version_number,omitempty"`
	// Serialized raw tensor content from either Tensor::AsProtoTensorContent or
	// memcpy in tensorflow::grpc::EncodeTensorToByteBuffer. This representation
	// can be used for all tensor types. The purpose of this representation is to
	// reduce serialization overhead during RPC call by avoiding serialization of
	// many repeated small items.
	TensorContent []byte `protobuf:"bytes,4,opt,name=tensor_content,json=tensorContent,proto3" json:"tensor_content,omitempty"`
	// DT_HALF, DT_BFLOAT16. Note that since protobuf has no int16 type, we'll
	// have some pointless zero padding for each value here.
	HalfVal []int32 `protobuf:"varint,13,rep,packed,name=half_val,json=halfVal,proto3" json:"half_val,omitempty"`
	// DT_FLOAT.
	FloatVal []float32 `protobuf:"fixed32,5,rep,packed,name=float_val,json=floatVal,proto3" json:"float_val,omitempty"`
	// DT_DOUBLE.
	DoubleVal []float64 `protobuf:"fixed64,6,rep,packed,name=double_val,json=doubleVal,proto3" json:"double_val,omitempty"`
	// DT_INT32, DT_INT16, DT_UINT16, DT_INT8, DT_UINT8.
	IntVal []int32 `protobuf:"varint,7,rep,packed,name=int_val,json=intVal,proto3" json:"int_val,omitempty"`
	// DT_STRING
	StringVal [][]byte `protobuf:"bytes,8,rep,name=string_val,json=stringVal,proto3" json:"string_val,omitempty"`
	// DT_COMPLEX64. scomplex_val(2*i) and scomplex_val(2*i+1) are real
	// and imaginary parts of i-th single precision complex.
	ScomplexVal []float32 `protobuf:"fixed32,9,rep,packed,name=scomplex_val,json=scomplexVal,proto3" json:"scomplex_val,omitempty"`
	// DT_INT64
	Int64Val []int64 `protobuf:"varint,10,rep,packed,name=int64_val,json=int64Val,proto3" json:"int64_val,omitempty"`
	// DT_BOOL
	BoolVal []bool `protobuf:"varint,11,rep,packed,name=bool_val,json=boolVal,proto3" json:"bool_val,omitempty"`
	// DT_COMPLEX128. dcomplex_val(2*i) and dcomplex_val(2*i+1) are real
	// and imaginary parts of i-th double precision complex.
	DcomplexVal []float64 `protobuf:"fixed64,12,rep,packed,name=dcomplex_val,json=dcomplexVal,proto3" json:"dcomplex_val,omitempty"`
	// DT_RESOURCE
	ResourceHandleVal []*ResourceHandleProto `protobuf:"bytes,14,rep,name=resource_handle_val,json=resourceHandleVal,proto3" json:"resource_handle_val,omitempty"`
	// DT_VARIANT
	VariantVal []*VariantTensorDataProto `protobuf:"bytes,15,rep,name=variant_val,json=variantVal,proto3" json:"variant_val,omitempty"`
	// DT_UINT32
	Uint32Val []uint32 `protobuf:"varint,16,rep,packed,name=uint32_val,json=uint32Val,proto3" json:"uint32_val,omitempty"`
	// DT_UINT64
	Uint64Val []uint64 `protobuf:"varint,17,rep,packed,name=uint64_val,json=uint64Val,proto3" json:"uint64_val,omitempty"`
	// DT_FLOAT8_*, use variable-sized set of bytes
	// (i.e. the equivalent of repeated uint8, if such a thing existed).
	Float8Val     []byte `protobuf:"bytes,18,opt,name=float8_val,json=float8Val,proto3" json:"float8_val,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TensorProto) Reset() {
	*x = TensorProto{}
	mi := &file_tensorflow_core_framework_tensor_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TensorProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorProto) ProtoMessage() {}

func (x *TensorProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_tensor_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorProto.ProtoReflect.Descriptor instead.
func (*TensorProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_tensor_proto_rawDescGZIP(), []int{0}
}

func (x *TensorProto) GetDtype() DataType {
	if x != nil {
		return x.Dtype
	}
	return DataType_DT_INVALID
}

func (x *TensorProto) GetTensorShape() *TensorShapeProto {
	if x != nil {
		return x.TensorShape
	}
	return nil
}

func (x *TensorProto) GetVersionNumber() int32 {
	if x != nil {
		return x.VersionNumber
	}
	return 0
}

func (x *TensorProto) GetTensorContent() []byte {
	if x != nil {
		return x.TensorContent
	}
	return nil
}

func (x *TensorProto) GetHalfVal() []int32 {
	if x != nil {
		return x.HalfVal
	}
	return nil
}

func (x *TensorProto) GetFloatVal() []float32 {
	if x != nil {
		return x.FloatVal
	}
	return nil
}

func (x *TensorProto) GetDoubleVal() []float64 {
	if x != nil {
		return x.DoubleVal
	}
	return nil
}

func (x *TensorProto) GetIntVal() []int32 {
	if x != nil {
		return x.IntVal
	}
	return nil
}

func (x *TensorProto) GetStringVal() [][]byte {
	if x != nil {
		return x.StringVal
	}
	return nil
}

func (x *TensorProto) GetScomplexVal() []float32 {
	if x != nil {
		return x.ScomplexVal
	}
	return nil
}

func (x *TensorProto) GetInt64Val() []int64 {
	if x != nil {
		return x.Int64Val
	}
	return nil
}

func (x *TensorProto) GetBoolVal() []bool {
	if x != nil {
		return x.BoolVal
	}
	return nil
}

func (x *TensorProto) GetDcomplexVal() []float64 {
	if x != nil {
		return x.DcomplexVal
	}
	return nil
}

func (x *TensorProto) GetResourceHandleVal() []*ResourceHandleProto {
	if x != nil {
		return x.ResourceHandleVal
	}
	return nil
}

func (x *TensorProto) GetVariantVal() []*VariantTensorDataProto {
	if x != nil {
		return x.VariantVal
	}
	return nil
}

func (x *TensorProto) GetUint32Val() []uint32 {
	if x != nil {
		return x.Uint32Val
	}
	return nil
}

func (x *TensorProto) GetUint64Val() []uint64 {
	if x != nil {
		return x.Uint64Val
	}
	return nil
}

func (x *TensorProto) GetFloat8Val() []byte {
	if x != nil {
		return x.Float8Val
	}
	return nil
}

// Protocol buffer representing the serialization format of DT_VARIANT tensors.
type VariantTensorDataProto struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name of the type of objects being serialized.
	TypeName string `protobuf:"bytes,1,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"`
	// Portions of the object that are not Tensors.
	Metadata []byte `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Tensors contained within objects being serialized.
	Tensors       []*TensorProto `protobuf:"bytes,3,rep,name=tensors,proto3" json:"tensors,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VariantTensorDataProto) Reset() {
	*x = VariantTensorDataProto{}
	mi := &file_tensorflow_core_framework_tensor_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VariantTensorDataProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VariantTensorDataProto) ProtoMessage() {}

func (x *VariantTensorDataProto) ProtoReflect() protoreflect.Message {
	mi := &file_tensorflow_core_framework_tensor_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VariantTensorDataProto.ProtoReflect.Descriptor instead.
func (*VariantTensorDataProto) Descriptor() ([]byte, []int) {
	return file_tensorflow_core_framework_tensor_proto_rawDescGZIP(), []int{1}
}

func (x *VariantTensorDataProto) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *VariantTensorDataProto) GetMetadata() []byte {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *VariantTensorDataProto) GetTensors() []*TensorProto {
	if x != nil {
		return x.Tensors
	}
	return nil
}

var File_tensorflow_core_framework_tensor_proto protoreflect.FileDescriptor

const file_tensorflow_core_framework_tensor_proto_rawDesc = "" +
	"\n" +
	"&tensorflow/core/framework/tensor.proto\x12\n" +
	"tensorflow\x1a/tensorflow/core/framework/resource_handle.proto\x1a,tensorflow/core/framework/tensor_shape.proto\x1a%tensorflow/core/framework/types.proto\"\xf0\x05\n" +
	"\vTensorProto\x12*\n" +
	"\x05dtype\x18\x01 \x01(\x0e2\x14.tensorflow.DataTypeR\x05dtype\x12?\n" +
	"\ftensor_shape\x18\x02 \x01(\v2\x1c.tensorflow.TensorShapeProtoR\vtensorShape\x12%\n" +
	"\x0eversion_number\x18\x03 \x01(\x05R\rversionNumber\x12%\n" +
	"\x0etensor_content\x18\x04 \x01(\fR\rtensorContent\x12\x1d\n" +
	"\bhalf_val\x18\r \x03(\x05B\x02\x10\x01R\ahalfVal\x12\x1f\n" +
	"\tfloat_val\x18\x05 \x03(\x02B\x02\x10\x01R\bfloatVal\x12!\n" +
	"\n" +
	"double_val\x18\x06 \x03(\x01B\x02\x10\x01R\tdoubleVal\x12\x1b\n" +
	"\aint_val\x18\a \x03(\x05B\x02\x10\x01R\x06intVal\x12\x1d\n" +
	"\n" +
	"string_val\x18\b \x03(\fR\tstringVal\x12%\n" +
	"\fscomplex_val\x18\t \x03(\x02B\x02\x10\x01R\vscomplexVal\x12\x1f\n" +
	"\tint64_val\x18\n" +
	" \x03(\x03B\x02\x10\x01R\bint64Val\x12\x1d\n" +
	"\bbool_val\x18\v \x03(\bB\x02\x10\x01R\aboolVal\x12%\n" +
	"\fdcomplex_val\x18\f \x03(\x01B\x02\x10\x01R\vdcomplexVal\x12O\n" +
	"\x13resource_handle_val\x18\x0e \x03(\v2\x1f.tensorflow.ResourceHandleProtoR\x11resourceHandleVal\x12C\n" +
	"\vvariant_val\x18\x0f \x03(\v2\".tensorflow.VariantTensorDataProtoR\n" +
	"variantVal\x12!\n" +
	"\n" +
	"uint32_val\x18\x10 \x03(\rB\x02\x10\x01R\tuint32Val\x12!\n" +
	"\n" +
	"uint64_val\x18\x11 \x03(\x04B\x02\x10\x01R\tuint64Val\x12\x1d\n" +
	"\n" +
	"float8_val\x18\x12 \x01(\fR\tfloat8Val\"\x84\x01\n" +
	"\x16VariantTensorDataProto\x12\x1b\n" +
	"\ttype_name\x18\x01 \x01(\tR\btypeName\x12\x1a\n" +
	"\bmetadata\x18\x02 \x01(\fR\bmetadata\x121\n" +
	"\atensors\x18\x03 \x03(\v2\x17.tensorflow.TensorProtoR\atensorsB_\n" +
	"\x18org.tensorflow.frameworkB\fTensorProtosP\x01Z0rtb_model_server/proto/tensorflow/core/framework\xf8\x01\x01b\x06proto3"

var (
	file_tensorflow_core_framework_tensor_proto_rawDescOnce sync.Once
	file_tensorflow_core_framework_tensor_proto_rawDescData []byte
)

func file_tensorflow_core_framework_tensor_proto_rawDescGZIP() []byte {
	file_tensorflow_core_framework_tensor_proto_rawDescOnce.Do(func() {
		file_tensorflow_core_framework_tensor_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_tensorflow_core_framework_tensor_proto_rawDesc), len(file_tensorflow_core_framework_tensor_proto_rawDesc)))
	})
	return file_tensorflow_core_framework_tensor_proto_rawDescData
}

var file_tensorflow_core_framework_tensor_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_tensorflow_core_framework_tensor_proto_goTypes = []any{
	(*TensorProto)(nil),            // 0: tensorflow.TensorProto
	(*VariantTensorDataProto)(nil), // 1: tensorflow.VariantTensorDataProto
	(DataType)(0),                  // 2: tensorflow.DataType
	(*TensorShapeProto)(nil),       // 3: tensorflow.TensorShapeProto
	(*ResourceHandleProto)(nil),    // 4: tensorflow.ResourceHandleProto
}
var file_tensorflow_core_framework_tensor_proto_depIdxs = []int32{
	2, // 0: tensorflow.TensorProto.dtype:type_name -> tensorflow.DataType
	3, // 1: tensorflow.TensorProto.tensor_shape:type_name -> tensorflow.TensorShapeProto
	4, // 2: tensorflow.TensorProto.resource_handle_val:type_name -> tensorflow.ResourceHandleProto
	1, // 3: tensorflow.TensorProto.variant_val:type_name -> tensorflow.VariantTensorDataProto
	0, // 4: tensorflow.VariantTensorDataProto.tensors:type_name -> tensorflow.TensorProto
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_tensorflow_core_framework_tensor_proto_init() }
func file_tensorflow_core_framework_tensor_proto_init() {
	if File_tensorflow_core_framework_tensor_proto != nil {
		return
	}
	file_tensorflow_core_framework_resource_handle_proto_init()
	file_tensorflow_core_framework_tensor_shape_proto_init()
	file_tensorflow_core_framework_types_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_tensorflow_core_framework_tensor_proto_rawDesc), len(file_tensorflow_core_framework_tensor_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tensorflow_core_framework_tensor_proto_goTypes,
		DependencyIndexes: file_tensorflow_core_framework_tensor_proto_depIdxs,
		MessageInfos:      file_tensorflow_core_framework_tensor_proto_msgTypes,
	}.Build()
	File_tensorflow_core_framework_tensor_proto = out.File
	file_tensorflow_core_framework_tensor_proto_goTypes = nil
	file_tensorflow_core_framework_tensor_proto_depIdxs = nil
}
