package main

import (
	"fmt"
	"log"
	"rtb_model_server/conf"
	"rtb_model_server/internal/mock"
	"rtb_model_server/internal/model_index/predict"
	"time"
)

func main() {
	// 初始化配置
	err := conf.ConfigInit("../conf/rtb_model_server.yaml")
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	fmt.Printf("Protocol Type: %s\n", conf.GlobalConfig.PredictServer.ProtocolType)

	// 启动mock服务器
	go func() {
		mockServer := &mock.PredictServer{}
		if err := mockServer.Start(); err != nil {
			log.Printf("Mock server error: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(2 * time.Second)

	// 创建连接池
	pool := predict.NewConnectionPool(
		conf.GlobalConfig.PredictServer.Addr,
		conf.GlobalConfig.PredictServer.PoolSize,
		conf.GlobalConfig.PredictServer.MaxIdleConns,
		time.Duration(conf.GlobalConfig.PredictServer.ConnTimeout)*time.Millisecond,
	)
	defer pool.Close()

	// 测试连接
	conn, err := pool.GetConnection()
	if err != nil {
		log.Printf("Failed to get connection: %v", err)
		return
	}

	fmt.Printf("Successfully created connection with %s protocol\n", conf.GlobalConfig.PredictServer.ProtocolType)
	pool.ReleaseConnection(conn)

	// 获取连接池统计
	total, inUse, idle := pool.GetPoolStats()
	fmt.Printf("Pool stats - Total: %d, InUse: %d, Idle: %d\n", total, inUse, idle)
}