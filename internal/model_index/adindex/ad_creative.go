package adindex

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// CreativeFilter 默认的Creative过滤器
func (i *AdIndexManager) CreativeFilter(creative IndexableCreative) bool {
	// 示例过滤逻辑：过滤掉状态异常的creative
	if creative.RTBCreative == nil {
		zaplog.Logger.Debug("creative is nil, filtering out", zap.Int32("id", creative.GetId()))
		return false
	}
	if creative.RTBCreative.Id < 0 {
		zaplog.Logger.Debug("creative id is nil, filtering out", zap.Int32("id", creative.GetId()))
		return false
	}

	if len(creative.RTBCreative.Containers) == 0 {
		zaplog.Logger.Debug("creative containers is empty, filtering out", zap.Int32("id", creative.GetId()))
		return false
	}

	if _, err := i.GetCampaign(creative.CampaignId); err != nil {
		zaplog.Logger.Debug("creative campaign not found, filtering out", zap.Int32("id", creative.GetId()))
		return false
	}

	if _, err := i.GetStrategy(creative.StrategyId); err != nil {
		zaplog.Logger.Debug("creative strategy not found, filtering out", zap.Int32("id", creative.GetId()))
		return false
	}

	if _, err := i.GetSponsor(creative.SponsorId); err != nil {
		zaplog.Logger.Debug("creative sponsor not found, filtering out", zap.Int32("id", creative.GetId()))
		return false
	}
	if len(creative.AdTrackingIds) == 0 || len(creative.PromotionIds) == 0 {
		zaplog.Logger.Debug("creative tracking or promotion count is 0, filtering out", zap.Int32("id", creative.GetId()))
		return false
	}
	if _, err := i.GetAdTracking(creative.AdTrackingIds[0]); err != nil {
		zaplog.Logger.Debug("creative tracking not found, filtering out", zap.Int32("id", creative.GetId()))
		return false
	}
	if _, err := i.GetPromotion(creative.PromotionIds[0]); err != nil {
		zaplog.Logger.Debug("creative promotion not found, filtering out", zap.Int32("id", creative.GetId()))
		return false
	}
	if creative.PauseStatus != 0 || creative.Status != 0 {
		return false
	}

	return true
}

// LoadCreative 加载Creative数据，使用默认过滤器
func (i *AdIndexManager) LoadCreative() (int, error) {
	return i.LoadCreativeWithFilter(i.CreativeFilter)
}

// LoadCreativeWithFilter 加载Creative数据，使用自定义过滤器
func (i *AdIndexManager) LoadCreativeWithFilter(filter FilterFunc[IndexableCreative]) (int, error) {
	versionedPath := i.getVersionedFilePath(i.config.AdIndexPath.AdCreative)
	return LoadAdIndexData(versionedPath, "creative", func() IndexableCreative {
		return IndexableCreative{RTBCreative: &rtb_adinfo_types.RTBCreative{}}
	}, filter)
}

// GetCreative 获取Creative数据
func (i *AdIndexManager) GetCreative(creativeId int32) (*rtb_adinfo_types.RTBCreative, error) {
	if data, ok := dictAdCreative.Load(creativeId); ok {
		return data.(IndexableCreative).RTBCreative, nil
	}
	return nil, fmt.Errorf("creative %d not found in index dict", creativeId)
}

// GetAllCreative 获取所有Creative数据
func (i *AdIndexManager) GetAllCreativeId() []int32 {
	var creativeList []int32
	dictAdCreative.Range(func(key, value any) bool {
		creativeList = append(creativeList, value.(IndexableCreative).RTBCreative.Id)
		return true
	})
	return creativeList
}
