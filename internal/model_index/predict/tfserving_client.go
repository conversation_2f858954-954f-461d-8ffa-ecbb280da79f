package predict

import (
	"context"
	"fmt"
	"time"

	"rtb_model_server/internal/zaplog"
	tensorflow "rtb_model_server/proto/tensorflow/core/framework"
	"rtb_model_server/proto/tensorflow_serving"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials/insecure"
)

// BatchPredictData 批量预测数据结构
type BatchPredictData struct {
	BatchSize      int                `json:"batch_size"`
	CreativeIDs    []int32            `json:"creative_ids"`    // 创意ID列表，保持顺序对齐
	RawFeatures    map[string][]int64 `json:"raw_features"`    // 每个特征是数组，支持批量
	HashedFeatures [][]uint64         `json:"hashed_features"` // 每个样本的hash特征列表（如果需要）
}

// HashFeatureNames 45个需要hash的特征名称（按slot_id顺序）
var HashFeatureNames = []string{
	"dsp_id", "exchange_id", "week_day", "hour", "inventory_type",
	"adp_id", "client_ip", "adp_dim", "bidfloor", "sponsor_id",
	"campaign_id", "strategy_id", "creative_id", "dsp_advertiser_id", "dsp_creative_id",
	"creative_type", "media_bid_type", "dm_platform", "template_id", "app_bundle",
	"ad_source", "province", "city", "standard_make", "dev_make",
	"dev_model", "exp_id", "dm_media_id", "absolute_pos", "dsp_ad_slot",
	"api_version", "tanx_task_id", "tanx_group_id", "tanx_ad_id", "client_ipv6",
	"country", "domob_bidfloor", "cat_id", "bid_id", "schain",
	"surge_score", "dsp_cost_mod", "budget_type_v1", "app_name", "app_package_name",
}

// TFServingClient TFServing客户端
type TFServingClient struct {
	conn       *grpc.ClientConn
	client     tensorflow_serving.PredictionServiceClient
	serverAddr string
	modelName  string
}

// NewTFServingClient 创建TFServing客户端
func NewTFServingClient(addr string, modelName string) (*TFServingClient, error) {
	zaplog.Logger.Info("Creating TensorFlow Serving gRPC client ",
		zap.String("server", addr),
		zap.String("model", modelName))

	// 创建gRPC连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	conn, err := grpc.DialContext(ctx, addr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(4*1024*1024)), // 4MB
		grpc.WithBlock(), // 等待连接建立
	)
	if err != nil {
		zaplog.Logger.Error("Failed to connect to TensorFlow Serving",
			zap.String("server", addr),
			zap.Error(err))
		return nil, fmt.Errorf("grpc connection failed: %w", err)
	}

	client := tensorflow_serving.NewPredictionServiceClient(conn)

	tfClient := &TFServingClient{
		conn:       conn,
		client:     client,
		serverAddr: addr,
		modelName:  modelName,
	}

	// 测试连接
	if err := tfClient.TestConnection(); err != nil {
		conn.Close()
		zaplog.Logger.Error("Failed to test TensorFlow Serving connection",
			zap.String("server", addr),
			zap.Error(err))
		return nil, fmt.Errorf("connection test failed: %w", err)
	}

	zaplog.Logger.Info("TensorFlow Serving gRPC client V2 created successfully",
		zap.String("server", addr))

	return tfClient, nil
}

// Close 关闭连接
func (c *TFServingClient) Close() {
	if c.conn != nil {
		c.conn.Close()
		zaplog.Logger.Info("TensorFlow Serving gRPC connection V2 closed")
	}
}

// TestConnection 测试连接状态
func (c *TFServingClient) TestConnection() error {
	if c.conn == nil {
		return fmt.Errorf("connection is nil")
	}

	state := c.conn.GetState()
	if state != connectivity.Ready && state != connectivity.Idle {
		return fmt.Errorf("connection not ready, state: %v", state)
	}

	return nil
}

// Predict 批量预测
func (c *TFServingClient) Predict(ctx context.Context, batchFeatures []map[string]interface{}) (map[string]interface{}, error) {
	if len(batchFeatures) == 0 {
		return nil, fmt.Errorf("empty batch features")
	}

	batchSize := len(batchFeatures)

	// 构造批量输入
	batchInputs := make(map[string]*tensorflow.TensorProto)

	// 收集所有特征名称
	allFeatureNames := make(map[string]bool)
	for _, features := range batchFeatures {
		for featureName := range features {
			allFeatureNames[featureName] = true
		}
	}

	// 为每个特征创建批量张量
	for featureName := range allFeatureNames {
		batchValues := make([]int64, batchSize)

		// 收集该特征在所有样本中的值
		for i, features := range batchFeatures {
			value, exists := features[featureName]
			if !exists {
				return nil, fmt.Errorf("missing required feature '%s' for batch item %d", featureName, i)
			}

			if intVal, ok := c.convertToInt64(value); ok {
				batchValues[i] = intVal
			} else {
				return nil, fmt.Errorf("failed to convert feature '%s' to int64 for batch item %d: %v", featureName, i, value)
			}
		}

		// 创建批量张量
		batchInputs[featureName] = &tensorflow.TensorProto{
			Dtype: tensorflow.DataType_DT_INT64,
			TensorShape: &tensorflow.TensorShapeProto{
				Dim: []*tensorflow.TensorShapeProto_Dim{
					{Size: int64(batchSize)},
				},
			},
			Int64Val: batchValues,
		}
	}

	// 构造请求
	request := &tensorflow_serving.PredictRequest{
		ModelSpec: &tensorflow_serving.ModelSpec{
			Name:          c.modelName,
			SignatureName: "serving_default",
		},
		Inputs: batchInputs,
	}

	zaplog.Logger.Debug("Sending batch predict request",
		zap.String("model", c.modelName),
		zap.Int("batch_size", batchSize),
		zap.Int("feature_count", len(batchInputs)))

	// 设置超时
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 调用预测
	response, err := c.client.Predict(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("batch predict failed: %w", err)
	}

	// 解析响应
	result := make(map[string]interface{})
	for outputName, tensor := range response.Outputs {
		values, err := c.parseTensorOutput(tensor)
		if err != nil {
			zaplog.Logger.Error("Failed to parse batch tensor output",
				zap.String("output", outputName),
				zap.Error(err))
			continue
		}
		result[outputName] = values
	}

	return result, nil
}

// createCompatibleTensor 创建兼容的张量 - 对齐go-bid-gateway-tom的方式
func (c *TFServingClient) createCompatibleTensor(featureName string, value interface{}) (*tensorflow.TensorProto, error) {
	switch v := value.(type) {
	case int64:
		// 单个int64值
		return &tensorflow.TensorProto{
			Dtype: tensorflow.DataType_DT_INT64,
			TensorShape: &tensorflow.TensorShapeProto{
				Dim: []*tensorflow.TensorShapeProto_Dim{
					{Size: 1}, // 单个值
				},
			},
			Int64Val: []int64{v},
		}, nil

	case []int64:
		// int64数组
		return &tensorflow.TensorProto{
			Dtype: tensorflow.DataType_DT_INT64,
			TensorShape: &tensorflow.TensorShapeProto{
				Dim: []*tensorflow.TensorShapeProto_Dim{
					{Size: int64(len(v))},
				},
			},
			Int64Val: v,
		}, nil

	case float32:
		// 单个float32值
		return &tensorflow.TensorProto{
			Dtype: tensorflow.DataType_DT_FLOAT,
			TensorShape: &tensorflow.TensorShapeProto{
				Dim: []*tensorflow.TensorShapeProto_Dim{
					{Size: 1},
				},
			},
			FloatVal: []float32{v},
		}, nil

	case []float32:
		// float32数组
		return &tensorflow.TensorProto{
			Dtype: tensorflow.DataType_DT_FLOAT,
			TensorShape: &tensorflow.TensorShapeProto{
				Dim: []*tensorflow.TensorShapeProto_Dim{
					{Size: int64(len(v))},
				},
			},
			FloatVal: v,
		}, nil

	case string:
		// 字符串值 - 参考go-bid-gateway-tom的StringVal处理
		return &tensorflow.TensorProto{
			Dtype: tensorflow.DataType_DT_STRING,
			TensorShape: &tensorflow.TensorShapeProto{
				Dim: []*tensorflow.TensorShapeProto_Dim{
					{Size: 1},
				},
			},
			// 注意：我们的简化proto可能没有StringVal字段
			// 这里需要根据实际proto定义调整
		}, fmt.Errorf("string type not supported in simplified proto")

	default:
		return nil, fmt.Errorf("unsupported value type: %T", value)
	}
}

// parseTensorOutput 解析张量输出
func (c *TFServingClient) parseTensorOutput(tensor *tensorflow.TensorProto) (interface{}, error) {
	switch tensor.Dtype {
	case tensorflow.DataType_DT_FLOAT:
		return tensor.FloatVal, nil
	case tensorflow.DataType_DT_DOUBLE:
		return tensor.DoubleVal, nil
	case tensorflow.DataType_DT_INT64:
		return tensor.Int64Val, nil
	default:
		return nil, fmt.Errorf("unsupported output dtype: %v", tensor.Dtype)
	}
}

// BatchPredictV2 批量预测 - 兼容版本
func (c *TFServingClient) BatchPredictV2(ctx context.Context, batchFeatures []map[string]interface{}) (map[string]interface{}, error) {
	if len(batchFeatures) == 0 {
		return nil, fmt.Errorf("empty batch features")
	}

	batchSize := len(batchFeatures)

	// 构造批量输入
	batchInputs := make(map[string]*tensorflow.TensorProto)

	// 收集所有特征名称
	allFeatureNames := make(map[string]bool)
	for _, features := range batchFeatures {
		for featureName := range features {
			allFeatureNames[featureName] = true
		}
	}

	// 为每个特征创建批量张量
	for featureName := range allFeatureNames {
		batchValues := make([]int64, batchSize)

		// 收集该特征在所有样本中的值
		for i, features := range batchFeatures {
			if value, exists := features[featureName]; exists {
				if intVal, ok := value.(int64); ok {
					batchValues[i] = intVal
				} else {
					batchValues[i] = 0
				}
			} else {
				batchValues[i] = 0
			}
		}

		// 创建批量张量
		batchInputs[featureName] = &tensorflow.TensorProto{
			Dtype: tensorflow.DataType_DT_INT64,
			TensorShape: &tensorflow.TensorShapeProto{
				Dim: []*tensorflow.TensorShapeProto_Dim{
					{Size: int64(batchSize)},
				},
			},
			Int64Val: batchValues,
		}
	}

	// 构造请求
	request := &tensorflow_serving.PredictRequest{
		ModelSpec: &tensorflow_serving.ModelSpec{
			Name:          c.modelName,
			SignatureName: "serving_default",
		},
		Inputs: batchInputs,
	}

	zaplog.Logger.Debug("Sending batch predict request",
		zap.String("model", c.modelName),
		zap.Int("batch_size", batchSize),
		zap.Int("feature_count", len(batchInputs)))

	// 调用预测
	response, err := c.client.Predict(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("batch predict failed: %w", err)
	}

	// 解析响应
	result := make(map[string]interface{})
	for outputName, tensor := range response.Outputs {
		values, err := c.parseTensorOutput(tensor)
		if err != nil {
			zaplog.Logger.Error("Failed to parse batch tensor output",
				zap.String("output", outputName),
				zap.Error(err))
			continue
		}
		result[outputName] = values
	}

	return result, nil
}

// convertToInt64 将任意类型转换为 int64 - 增强版本
func (c *TFServingClient) convertToInt64(value interface{}) (int64, bool) {
	switch v := value.(type) {
	case int64:
		return v, true
	case int32:
		return int64(v), true
	case int:
		return int64(v), true
	case float64:
		return int64(v), true
	case float32:
		return int64(v), true
	default:
		return 0, false
	}
}

// createTensorFromBatch 从批量值创建张量 - 通用版本
func (c *TFServingClient) createTensorFromBatch(featureName string, batchValues []interface{}) (*tensorflow.TensorProto, error) {
	if len(batchValues) == 0 {
		return nil, fmt.Errorf("empty batch values for feature %s", featureName)
	}

	batchSize := int64(len(batchValues))

	// 检查第一个非空值来确定数据类型
	var sampleValue interface{}
	for _, value := range batchValues {
		if value != nil {
			sampleValue = value
			break
		}
	}

	if sampleValue == nil {
		return nil, fmt.Errorf("all values are nil for feature %s", featureName)
	}

	switch sampleValue.(type) {
	case []int64:
		// 序列特征：shape=(batch_size, 序列长度)
		return c.createSequenceTensor(batchValues, batchSize, true)
	case int64, int32, int:
		// 单特征：shape=(batch_size,)
		return c.createSingleTensor(batchValues, batchSize, true)
	case []float32:
		// 序列特征：shape=(batch_size, 序列长度)
		return c.createSequenceTensor(batchValues, batchSize, false)
	case float32, float64:
		// 单特征：shape=(batch_size,)
		return c.createSingleTensor(batchValues, batchSize, false)
	default:
		return nil, fmt.Errorf("unsupported batch type for feature %s: %T", featureName, sampleValue)
	}
}

// createSingleTensor 创建单特征张量
func (c *TFServingClient) createSingleTensor(batchValues []interface{}, batchSize int64, isInt bool) (*tensorflow.TensorProto, error) {
	tensor := &tensorflow.TensorProto{
		TensorShape: &tensorflow.TensorShapeProto{
			Dim: []*tensorflow.TensorShapeProto_Dim{{Size: batchSize}},
		},
	}

	if isInt {
		tensor.Dtype = tensorflow.DataType_DT_INT64
		values := make([]int64, batchSize)
		for i, value := range batchValues {
			if intVal, ok := c.convertToInt64(value); ok {
				values[i] = intVal
			}
		}
		tensor.Int64Val = values
	} else {
		tensor.Dtype = tensorflow.DataType_DT_FLOAT
		values := make([]float32, batchSize)
		for i, value := range batchValues {
			switch v := value.(type) {
			case float32:
				values[i] = v
			case float64:
				values[i] = float32(v)
			}
		}
		tensor.FloatVal = values
	}

	return tensor, nil
}

// createSequenceTensor 创建序列特征张量
func (c *TFServingClient) createSequenceTensor(batchValues []interface{}, batchSize int64, isInt bool) (*tensorflow.TensorProto, error) {
	// 找到最大序列长度
	maxSeqLen := int64(0)
	for _, value := range batchValues {
		if isInt {
			if seq, ok := value.([]int64); ok && int64(len(seq)) > maxSeqLen {
				maxSeqLen = int64(len(seq))
			}
		} else {
			if seq, ok := value.([]float32); ok && int64(len(seq)) > maxSeqLen {
				maxSeqLen = int64(len(seq))
			}
		}
	}

	tensor := &tensorflow.TensorProto{
		TensorShape: &tensorflow.TensorShapeProto{
			Dim: []*tensorflow.TensorShapeProto_Dim{
				{Size: batchSize},
				{Size: maxSeqLen},
			},
		},
	}

	if isInt {
		tensor.Dtype = tensorflow.DataType_DT_INT64
		allValues := make([]int64, 0, batchSize*maxSeqLen)
		for _, value := range batchValues {
			if seq, ok := value.([]int64); ok {
				allValues = append(allValues, seq...)
				// 填充到最大长度
				for i := len(seq); i < int(maxSeqLen); i++ {
					allValues = append(allValues, 0)
				}
			} else {
				// 填充默认值
				for i := int64(0); i < maxSeqLen; i++ {
					allValues = append(allValues, 0)
				}
			}
		}
		tensor.Int64Val = allValues
	} else {
		tensor.Dtype = tensorflow.DataType_DT_FLOAT
		allValues := make([]float32, 0, batchSize*maxSeqLen)
		for _, value := range batchValues {
			if seq, ok := value.([]float32); ok {
				allValues = append(allValues, seq...)
				for i := len(seq); i < int(maxSeqLen); i++ {
					allValues = append(allValues, 0.0)
				}
			} else {
				for i := int64(0); i < maxSeqLen; i++ {
					allValues = append(allValues, 0.0)
				}
			}
		}
		tensor.FloatVal = allValues
	}

	return tensor, nil
}
