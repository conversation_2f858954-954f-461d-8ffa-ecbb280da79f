package mock

import (
	"fmt"
	"math/rand"
	"rtb_model_server/common/domob_thrift/predict_model_server"
	"rtb_model_server/conf"
	"time"

	"log"

	"git.apache.org/thrift.git/lib/go/thrift"
)

type PredictServerHandler struct {
}

func (h *PredictServerHandler) GetPredictValue(ms_request *predict_model_server.PredictModelServerRequest) (r *predict_model_server.PredictModelServerResponse, err error) {
	t1 := time.Now()
	log.Println("GetPredictValue req_id", ms_request.ReqId, "search_id", ms_request.SearchId, "count", len(ms_request.AdList), time.Now().UnixMicro())
	resp := &predict_model_server.PredictModelServerResponse{
		Status:       0,
		SearchId:     ms_request.SearchId,
		ResponseList: make([]*predict_model_server.PredictResponseItem, 0),
	}
	for idx, feature := range ms_request.AdList {
		item := &predict_model_server.PredictResponseItem{
			AdIndex: int32(idx),
			Cid:     int32(feature.CreativeId),
			Ctr:     int64(10000 * rand.Float32()),
			Cvr:     int64(10000 * rand.Float32()),
			DeepCvr: int64(10000 * rand.Float32()),
		}
		resp.ResponseList = append(resp.ResponseList, item)
	}
	resp.PredictTs = int64(time.Since(t1).Microseconds())
	log.Println("GetPredictValue return", ms_request.ReqId, "search_id", ms_request.SearchId, "count", len(ms_request.AdList), time.Now().UnixMicro())
	return resp, nil
}

type PredictServer struct {
}

func (m *PredictServer) Start() error {
	listenAddr := fmt.Sprintf(":3399")
	log.Println("starting predict server", listenAddr)

	// 创建Thrift传输层
	transport, err := thrift.NewTServerSocket(listenAddr)
	if err != nil {
		return err
	}

	// 创建协议工厂
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()

	// 创建处理器
	handler := &PredictServerHandler{}
	processor := predict_model_server.NewPredictModelServerProcessor(handler)

	// 根据配置选择传输层工厂
	var serverTransportFactory thrift.TTransportFactory
	if conf.GlobalConfig.PredictServer.ProtocolType == "buffered" {
		// 使用buffered传输层
		serverTransportFactory = thrift.NewTBufferedTransportFactory(8192)
		log.Println("predict server using buffered transport")
	} else {
		// 使用默认传输层
		serverTransportFactory = thrift.NewTTransportFactory()
		log.Println("predict server using binary transport")
	}

	// 创建服务器
	server := thrift.NewTSimpleServer4(processor, transport, serverTransportFactory, protocolFactory)

	log.Println("predict server started successfully")
	if err := server.Serve(); err != nil {
		return err
	}
	return nil
}
